import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'app.dentalclinicinvellore.in',
      },
      {
        protocol: 'https',
        hostname: 'wp.dentaloffice.io',
      },
      {
        protocol: 'https',
        hostname: 'secure.gravatar.com',
      },
      {
        protocol: 'https',
        hostname: 'gravatar.com',
      },
      {
        protocol: 'https',
        hostname: '0.gravatar.com',
      },
      {
        protocol: 'https',
        hostname: '1.gravatar.com',
      },
      {
        protocol: 'https',
        hostname: '2.gravatar.com',
      },
    ],
  },
  // Add experimental features to handle navigation issues
  experimental: {
    // Disable prefetching to prevent fetch errors during navigation
    optimisticClientCache: false,
  },
  // Add redirects for external URLs
  async redirects() {
    return [
      {
        source: '/book-appointment',
        destination: 'https://app.dentalclinicinvellore.in/booking/',
        permanent: false,
      },
      {
        source: '/dashboard',
        destination: 'https://app.dentalclinicinvellore.in/dashboard',
        permanent: false,
      },
    ];
  },
};

export default nextConfig;
