# Website Accessibility Features

## Overview
This document describes the accessibility features implemented for the Indira Dental Clinic website. The implementation follows WCAG 2.1 guidelines and provides comprehensive accessibility support without breaking existing website functionality.

## Accessibility Widget

### Location
- **Position**: Fixed in the left bottom corner of the screen
- **Z-index**: 9999 (ensures it's always visible)
- **Responsive**: Works on all device sizes

### Features Included

#### 1. High Contrast Mode
- **Purpose**: Improves visibility for users with visual impairments
- **Implementation**: 
  - Black background with white text
  - Yellow buttons and links for maximum contrast
  - White borders for better element definition
- **Toggle**: Click "High Contrast" in the accessibility menu

#### 2. Font Size Scale
- **Purpose**: Simple 3-level text sizing for different user preferences
- **Options**:
  - **Small Text** (0.75x): Compact text for users who prefer smaller fonts
  - **Normal Text** (1.0x): Default website sizing
  - **Large Text** (1.2x): Larger text for better readability
- **Implementation**:
  - Scales text elements proportionally
  - Maintains minimum readability standards for small text
  - Preserves layout integrity at all sizes
  - Ensures buttons remain usable even at small scale
- **Controls**: Three-button scale (S, M, L) in the accessibility menu

#### 3. Remove Animations
- **Purpose**: Reduces animations to minimal levels for users sensitive to motion while maintaining website functionality
- **Implementation**:
  - Reduces all animation durations to 0.05s (barely noticeable)
  - Minimizes hover effects (scale reduced to 1.01x, subtle shadows)
  - Keeps color changes but makes them very quick
  - Maintains focus indicators with fast transitions
  - Disables smooth scrolling
  - Preserves website functionality while reducing motion
- **Toggle**: Click "Remove Animations" in the accessibility menu

#### 4. Reset All
- **Purpose**: Quickly return to default settings
- **Implementation**: Resets all accessibility features to off

## Keyboard Navigation

### Features
- **Escape Key**: Closes open menus and modals
- **Tab Navigation**: All interactive elements are keyboard accessible
- **Focus Indicators**: Enhanced focus outlines for better visibility
- **Skip Links**: "Skip to main content" link for screen readers

### Enhanced Focus Indicators
- **High Contrast Mode**: 3px cyan outline with 2px offset
- **Normal Mode**: Standard browser focus indicators
- **Accessibility Toggle**: Green outline when focused

## Screen Reader Support

### ARIA Labels and Attributes
- **Mobile Menu Button**: 
  - `aria-label="Toggle mobile menu"`
  - `aria-expanded` state
  - `aria-controls` pointing to navigation
- **Navigation**: 
  - `role="navigation"`
  - `aria-label="Mobile navigation"`
- **Modal Dialogs**: 
  - `role="dialog"`
  - `aria-modal="true"`
  - `aria-labelledby` for titles
- **Accessibility Menu**: 
  - `role="dialog"`
  - `aria-label="Accessibility Options"`
  - `aria-pressed` states for toggle buttons

### Screen Reader Only Content
- **Skip Link**: Hidden until focused, allows jumping to main content
- **Button States**: Proper announcement of toggle states

## Technical Implementation

### Files Modified
1. **`app/globals.css`**: Added accessibility styles and CSS variables
2. **`components/ui/AccessibilityWidget.tsx`**: Main accessibility component
3. **`app/layout.tsx`**: Added widget to layout and main content ID
4. **`components/ui/header/main.tsx`**: Enhanced with ARIA attributes and keyboard support

### CSS Classes Added
- `.accessibility-active`: High contrast mode styles
- `.font-scale-active`: Font scaling mode styles
- `.accessibility-toggle`: Toggle button styles
- `.accessibility-menu`: Menu panel styles
- `.sr-only`: Screen reader only content
- `.skip-link`: Skip to content link

### CSS Variables
- `--accessibility-font-scale`: Controls the font size multiplier (0.75, 1.0, 1.2)
- `--font-scale`: Local variable for refined scaling calculations

### Local Storage
- **Persistence**: User preferences are saved to localStorage
- **Key**: `accessibility-preferences`
- **Data**: JSON object with feature states (highContrast, fontSize, removeAnimations)

## Browser Compatibility
- **Modern Browsers**: Full support (Chrome, Firefox, Safari, Edge)
- **Older Browsers**: Graceful degradation
- **Mobile**: Full touch and keyboard support

## Testing Recommendations

### Manual Testing
1. **Keyboard Navigation**: Tab through all elements
2. **Screen Reader**: Test with NVDA, JAWS, or VoiceOver
3. **High Contrast**: Verify all content is visible
4. **Large Text**: Check layout doesn't break
5. **Reduced Motion**: Confirm animations are disabled

### Automated Testing
- Use tools like axe-core or Lighthouse accessibility audit
- Test color contrast ratios
- Validate ARIA attributes

## Usage Instructions

### For Users
1. **Access**: Click the accessibility icon (♿) in the bottom left corner
2. **Toggle Features**: Click any feature button to enable/disable
3. **Reset**: Use "Reset All" to return to defaults
4. **Persistence**: Settings are remembered between visits

### For Developers
1. **Customization**: Modify CSS variables in `globals.css`
2. **New Features**: Add to `AccessibilityWidget.tsx`
3. **Styling**: Update `.accessibility-menu` styles as needed

## Compliance
- **WCAG 2.1 Level AA**: Meets most requirements
- **Section 508**: Compatible
- **ADA**: Supports compliance efforts

## Recent Enhancements (v2.0)
- **Font Size Scale**: Replaced simple large text toggle with 5-level scaling system
- **Component Scaling**: All UI elements scale proportionally with text
- **Smaller Text Option**: Added ability to reduce text size below normal
- **Remove Animations**: Renamed from "Reduce Motion" for clarity
- **Enhanced Controls**: Intuitive button layout (S, M, L, XL, XXL)

## Future Enhancements
- Voice navigation support
- Additional color themes
- Font family options
- Reading mode
- Focus management improvements
- Dyslexia-friendly fonts
