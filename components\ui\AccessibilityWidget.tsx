'use client';

import { useState, useEffect } from 'react';

interface AccessibilityState {
  highContrast: boolean;
  fontSize: number; // Scale: 0.8, 1.0, 1.2, 1.5, 1.8
  removeAnimations: boolean;
  isMenuOpen: boolean;
}

export default function AccessibilityWidget() {
  const [state, setState] = useState<AccessibilityState>({
    highContrast: false,
    fontSize: 1.0,
    removeAnimations: false,
    isMenuOpen: false,
  });

  // Font size options - simplified to 3 options
  const fontSizeOptions = [
    { value: 0.75, label: 'Small Text' },
    { value: 1.0, label: 'Normal Text' },
    { value: 1.2, label: 'Large Text' }
  ];

  // Load saved preferences from localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem('accessibility-preferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        setState(prev => ({ ...prev, ...preferences }));
      } catch (error) {
        console.error('Error loading accessibility preferences:', error);
      }
    }
  }, []);

  // Apply accessibility features to document
  useEffect(() => {
    const { highContrast, fontSize, removeAnimations } = state;

    // High contrast mode
    if (highContrast) {
      document.documentElement.classList.add('accessibility-active');
    } else {
      document.documentElement.classList.remove('accessibility-active');
    }

    // Font size scaling
    document.documentElement.style.setProperty('--accessibility-font-scale', fontSize.toString());
    if (fontSize !== 1.0) {
      document.documentElement.classList.add('font-scale-active');
    } else {
      document.documentElement.classList.remove('font-scale-active');
    }

    // Reduce animations - make them very subtle instead of removing completely
    if (removeAnimations) {
      const style = document.createElement('style');
      style.id = 'remove-animations-style';
      style.textContent = `
        *, *::before, *::after {
          animation-duration: 0.05s !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.05s !important;
          transition-delay: 0ms !important;
        }

        /* Reduce hover effects to minimal */
        .hover\\:scale-105:hover {
          transform: scale(1.01) !important;
          transition-duration: 0.05s !important;
        }

        .hover\\:shadow-xl:hover,
        .hover\\:shadow-2xl:hover {
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
          transition-duration: 0.05s !important;
        }

        /* Keep color changes but make them instant */
        .hover\\:bg-\\[\\#C7205D\\]:hover,
        .hover\\:text-\\[\\#E53274\\]:hover,
        .hover\\:border-\\[\\#C7205D\\]:hover {
          transition-duration: 0.05s !important;
        }

        /* Reduce group hover effects */
        .group:hover .group-hover\\:border-\\[\\#E53274\\] {
          transition-duration: 0.05s !important;
        }

        /* Make focus changes very quick */
        *:focus,
        *:focus-visible {
          transition-duration: 0.05s !important;
        }

        /* Disable smooth scrolling */
        html {
          scroll-behavior: auto !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      const existingStyle = document.getElementById('remove-animations-style');
      if (existingStyle) {
        existingStyle.remove();
      }
    }

    // Save preferences to localStorage
    const preferences = { highContrast, fontSize, removeAnimations };
    localStorage.setItem('accessibility-preferences', JSON.stringify(preferences));
  }, [state.highContrast, state.fontSize, state.removeAnimations]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.accessibility-widget')) {
        setState(prev => ({ ...prev, isMenuOpen: false }));
      }
    };

    if (state.isMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [state.isMenuOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Close menu on Escape
      if (event.key === 'Escape' && state.isMenuOpen) {
        setState(prev => ({ ...prev, isMenuOpen: false }));
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [state.isMenuOpen]);

  const toggleMenu = () => {
    setState(prev => ({ ...prev, isMenuOpen: !prev.isMenuOpen }));
  };

  const toggleFeature = (feature: keyof Omit<AccessibilityState, 'isMenuOpen' | 'fontSize'>) => {
    setState(prev => ({ ...prev, [feature]: !prev[feature] }));
  };

  const changeFontSize = (newSize: number) => {
    setState(prev => ({ ...prev, fontSize: newSize }));
  };

  const resetAll = () => {
    setState(prev => ({
      ...prev,
      highContrast: false,
      fontSize: 1.0,
      removeAnimations: false,
    }));
  };

  return (
    <div className="accessibility-widget">
      {/* Skip to main content link */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Accessibility toggle button */}
      <button
        className="accessibility-toggle"
        onClick={toggleMenu}
        aria-label="Open accessibility options"
        aria-expanded={state.isMenuOpen}
        aria-haspopup="true"
        title="Accessibility Options"
      >
        <i className="fas fa-universal-access" aria-hidden="true"></i>
      </button>

      {/* Accessibility menu */}
      <div 
        className={`accessibility-menu ${state.isMenuOpen ? 'open' : ''}`}
        role="dialog"
        aria-label="Accessibility Options"
        aria-hidden={!state.isMenuOpen}
      >
        <h3>Accessibility Options</h3>
        
        <button
          onClick={() => toggleFeature('highContrast')}
          className={state.highContrast ? 'active' : ''}
          aria-pressed={state.highContrast}
          title="Toggle high contrast mode for better visibility"
        >
          <i className="fas fa-adjust" aria-hidden="true"></i>
          {' '}High Contrast
        </button>

        {/* Font Size Scale */}
        <div style={{ marginTop: '8px', borderTop: '1px solid #dee2e6', paddingTop: '12px' }}>
          <div style={{ marginBottom: '8px', fontSize: '12px', fontWeight: '600', color: '#E53274' }}>
            Font Size: {fontSizeOptions.find(opt => opt.value === state.fontSize)?.label}
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {fontSizeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => changeFontSize(option.value)}
                className={state.fontSize === option.value ? 'active' : ''}
                style={{
                  flex: '1',
                  minWidth: '45px',
                  fontSize: '11px',
                  padding: '4px 6px'
                }}
                title={`Set font size to ${option.label.toLowerCase()}`}
              >
                {option.value === 0.75 ? 'S' :
                 option.value === 1.0 ? 'M' : 'L'}
              </button>
            ))}
          </div>
        </div>

        <button
          onClick={() => toggleFeature('removeAnimations')}
          className={state.removeAnimations ? 'active' : ''}
          aria-pressed={state.removeAnimations}
          title="Remove all animations and transitions"
        >
          <i className="fas fa-ban" aria-hidden="true"></i>
          {' '}Remove Animations
        </button>

        <button
          onClick={resetAll}
          title="Reset all accessibility settings to default"
          style={{ marginTop: '8px', borderTop: '1px solid #dee2e6', paddingTop: '12px' }}
        >
          <i className="fas fa-undo" aria-hidden="true"></i>
          {' '}Reset All
        </button>
      </div>
    </div>
  );
}
