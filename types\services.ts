export interface ServicePost {
  id: number;
  date: string;
  date_gmt: string;
  guid: {
    rendered: string;
  };
  modified: string;
  modified_gmt: string;
  slug: string;
  status: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    protected: boolean;
  };
  excerpt: {
    rendered: string;
    protected: boolean;
  };
  featured_media: number;
  template: string;
  meta: {
    _acf_changed: boolean;
  };
  class_list: string[];
  acf: {
    treatments_procedures?: string;
    facilities_technologies?: string;
    question_1?: string;
    answer_1?: string;
    question_2?: string;
    answer_2?: string;
    question_3?: string;
    answer_3?: string;
    question_4?: string;
    answer_4?: string;
    question_5?: string;
    answer_5?: string;
    question_6?: string;
    answer_6?: string;
    question_7?: string;
    answer_7?: string;
    question_8?: string;
    answer_8?: string;
    question_9?: string;
    answer_9?: string;
    question_10?: string;
    answer_10?: string;
    faq?: Array<{ question: string; answer: string }>;
    [key: string]: any;
  };
  _links: any;
}

export interface ServiceCardData {
  id: number;
  title: string;
  excerpt: string;
  slug: string;
  icon: string;
  content?: string;
  acf?: {
    treatments_procedures?: string;
    facilities_technologies?: string;
    question_1?: string;
    answer_1?: string;
    question_2?: string;
    answer_2?: string;
    question_3?: string;
    answer_3?: string;
    question_4?: string;
    answer_4?: string;
    question_5?: string;
    answer_5?: string;
    question_6?: string;
    answer_6?: string;
    question_7?: string;
    answer_7?: string;
    question_8?: string;
    answer_8?: string;
    question_9?: string;
    answer_9?: string;
    question_10?: string;
    answer_10?: string;
    faq?: Array<{ question: string; answer: string }>;
    [key: string]: any;
  };
}
