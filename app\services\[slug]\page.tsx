'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import CallBackForm from '@/components/ui/CallBackForm';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import { ServicePost } from '@/types/services';
import { fetchServiceBySlug } from '@/utils/services-api';

export default function ServicePage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [service, setService] = useState<ServicePost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // FAQ state management
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [contentSections, setContentSections] = useState<Array<{id: string, title: string, content: string}>>([]);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // Parse content sections from service content - Focus on H2 headings
  const parseContentSections = (htmlContent: string) => {
    const sections: Array<{id: string, title: string, content: string}> = [];

    // Split content by H2 tags specifically since that's what comes from REST API
    const h2Regex = /<h2[^>]*>(.*?)<\/h2>/gi;
    const parts = htmlContent.split(h2Regex);

    // Process each H2 heading and its following content
    for (let i = 1; i < parts.length; i += 2) {
      const title = parts[i].trim().replace(/<[^>]*>/g, ''); // Remove HTML tags from title
      const content = parts[i + 1] ? parts[i + 1].trim() : '';

      if (title && title.length > 2) { // Ensure meaningful titles
        // Create clean ID for CSS anchor navigation
        const id = title.toLowerCase()
          .replace(/[^a-z0-9\s]/g, '') // Remove special characters
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
          .substring(0, 50); // Limit ID length

        sections.push({
          id,
          title,
          content: content || '' // Content can be empty if it's just a heading
        });
      }
    }

    // If no H2 sections found, create a single overview section
    if (sections.length === 0 && htmlContent.trim()) {
      sections.push({
        id: 'overview',
        title: 'Overview',
        content: htmlContent
      });
    }

    return sections;
  };

  // Scroll to hero form function
  const scrollToHeroForm = () => {
    const heroSection = document.getElementById('hero-form');
    if (heroSection) {
      heroSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };



  // Consistent Call Back Button Component
  const CallBackButton = ({ children, className = "", onClick }: {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
  }) => (
    <button
      className={`bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold hover:shadow-lg hover:scale-105 border-2 border-white ${className}`}
      style={{ fontFamily: 'DM Sans, sans-serif' }}
      onClick={onClick || scrollToHeroForm}
    >
      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
      </svg>
      {children}
    </button>
  );

  // Fetch service data
  useEffect(() => {
    const fetchServiceData = async () => {
      try {
        setLoading(true);
        setError(null);
        const serviceData = await fetchServiceBySlug(slug);
        if (serviceData) {
          setService(serviceData);
          // Parse content sections
          const sections = parseContentSections(serviceData.content.rendered);
          setContentSections(sections);
        } else {
          setError('Service not found');
        }
      } catch (error) {
        console.error('Error fetching service data:', error);
        setError('Failed to load service');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchServiceData();
    }
  }, [slug]);

  // Helper function to extract text from HTML content
  const extractTextFromHtml = (html: string): string => {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#039;/g, "'") // Replace &#039; with '
      .trim();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#E53274] mx-auto mb-4"></div>
          <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Loading service details...
          </p>
        </div>
      </div>
    );
  }

  if (error || !service) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Service Not Found
          </h1>
          <p className="text-lg mb-8" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            {error || 'The requested service could not be found.'}
          </p>
          <Link href="/services" 
                className="inline-block bg-[#E53274] hover:bg-[#C7205D] text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300"
                style={{ fontFamily: 'Inter, sans-serif' }}>
            View All Services
          </Link>
        </div>
      </div>
    );
  }

  const excerpt = extractTextFromHtml(service.excerpt.rendered) || 
                 extractTextFromHtml(service.content.rendered).substring(0, 200) + '...';

  return (
    <>
      {/* CSS for smooth scrolling and offset for sticky headers */}
      <style jsx>{`
        html {
          scroll-behavior: smooth;
        }

        /* Offset for sticky headers when using anchor links */
        section[id] {
          scroll-margin-top: 170px; /* Main header (90px) + Service nav (60px) + buffer (20px) */
        }

        /* Remove underline from navigation links */
        .no-underline {
          text-decoration: none !important;
        }

        .no-underline:hover {
          text-decoration: none !important;
        }
      `}</style>

      <div className="min-h-screen service-page">
      {/* Hero Section */}
      <section
        id="hero-form"
        className="relative py-8 md:py-12 lg:py-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center"
        style={{
          background: 'linear-gradient(to bottom, #E53274 0%, #E53274 70%, #575C8D 100%)'
        }}
      >
        <div className="max-w-7xl mx-auto w-full">
          {/* Mobile Layout - Stacked */}
          <div className="block md:hidden">
            {/* Mobile Content */}
            <div className="text-[#FDFDFD] text-center mb-8">
              <h1 className="text-3xl font-bold leading-tight mb-4" style={{ fontFamily: 'DM Sans', fontWeight: 700 }}>
                {service.title.rendered} in Vellore
              </h1>

              <div className="space-y-3 text-base max-w-lg mx-auto" style={{ fontFamily: 'Inter', fontWeight: 500 }}>
                <p style={{ fontWeight: 500 }}>
                  {excerpt}
                </p>
              </div>

              {/* YouTube Video */}
              <div className="mt-6">
                <div className="relative w-full max-w-md mx-auto" style={{ paddingBottom: '56.25%' }}>
                  <iframe
                    className="absolute top-0 left-0 w-full h-full rounded-lg"
                    src="https://www.youtube.com/embed/cR4f7FXuSfc"
                    title="Dental Treatment Video"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
              </div>
            </div>

            {/* Mobile Form */}
            <div className="flex justify-center w-full">
              <div className="w-full max-w-sm">
                <CallBackForm variant="hero" />
              </div>
            </div>
          </div>

          {/* Tablet and Desktop Layout - Side by Side */}
          <div className="hidden md:grid md:grid-cols-1 lg:grid-cols-2 gap-6 md:gap-10 lg:gap-12 items-center">
            {/* Left Content */}
            <div className="text-[#FDFDFD] order-2 md:order-1 lg:order-1 text-center md:text-left">
              <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-4 md:mb-6" style={{ fontFamily: 'DM Sans', fontWeight: 700 }}>
                {service.title.rendered} in Vellore
              </h1>

              <div className="space-y-3 md:space-y-4 text-lg lg:text-xl max-w-2xl md:mx-auto lg:mx-0 mb-6" style={{ fontFamily: 'Inter', fontWeight: 500 }}>
                <p style={{ fontWeight: 500 }}>
                  {excerpt}
                </p>
              </div>

              {/* YouTube Video */}
              <div className="mb-6">
                <div className="relative w-full max-w-lg mx-auto md:mx-0" style={{ paddingBottom: '56.25%' }}>
                  <iframe
                    className="absolute top-0 left-0 w-full h-full rounded-lg"
                    src="https://www.youtube.com/embed/cR4f7FXuSfc"
                    title="Dental Treatment Video"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
              </div>
            </div>

            {/* Right Form */}
            <div className="flex justify-center order-1 md:order-2 lg:order-2 w-full">
              <div className="w-full max-w-lg lg:max-w-md">
                <CallBackForm variant="hero" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 3 Positive Cards Section */}
      <section className="py-8 md:py-12 lg:py-16 xl:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
            {/* Card 1 - You'll Feel at Ease */}
            <div
              className="rounded-xl md:rounded-2xl p-4 md:p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Dentist Icon */}
              <div className="flex justify-center mb-3 md:mb-4">
                <Image
                  src="/dentist.svg"
                  alt="Dentist"
                  width={48}
                  height={48}
                  className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                />
              </div>

              <h3
                className="text-lg md:text-xl font-bold mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                You'll Feel at Ease
              </h3>

              <p
                className="text-sm md:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                Our team explains everything gently — no stress, no surprises.
              </p>
            </div>

            {/* Card 2 - Know What You Pay */}
            <div
              className="rounded-2xl p-6 md:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Rupee Icon */}
              <div className="flex justify-center mb-4">
                <Image
                  src="/rupee.svg"
                  alt="Rupee"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
              </div>

              <h3
                className="text-lg md:text-xl font-bold mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                Know What You Pay
              </h3>

              <p
                className="text-sm md:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                No hidden charges, clear options, and affordable treatment plans.
              </p>
            </div>

            {/* Card 3 - Pain? Not Here */}
            <div
              className="rounded-2xl p-6 md:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Smile Icon */}
              <div className="flex justify-center mb-4">
                <Image
                  src="/smile.svg"
                  alt="Smile"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
              </div>

              <h3
                className="text-lg md:text-xl font-bold mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                Pain? Not Here
              </h3>

              <p
                className="text-sm md:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                From cleanings to root canals — comfort is our #1 focus.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Bar - Sticky below main header */}
      <section className="sticky top-[90px] z-[9998] py-3 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#E53274' }}>
        <div className="max-w-7xl mx-auto">
          <nav className="flex flex-wrap justify-center gap-4 md:gap-8">
            {/* Dynamic content sections from H2 headings */}
            {contentSections.length > 0 ? (
              contentSections.map((section, index) => (
                <React.Fragment key={section.id}>
                  <a
                    href={`#${section.id}`}
                    className="text-white hover:text-gray-200 transition-colors duration-300 text-sm md:text-base font-medium cursor-pointer no-underline"
                    style={{ fontFamily: 'Inter, sans-serif' }}
                  >
                    {section.title}
                  </a>
                  {(index < contentSections.length - 1) && (
                    <span className="text-white hidden md:inline">|</span>
                  )}
                </React.Fragment>
              ))
            ) : (
              // Fallback navigation if no H2 sections found
              <>
                <a
                  href="#overview"
                  className="text-white hover:text-gray-200 transition-colors duration-300 text-sm md:text-base font-medium cursor-pointer no-underline"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Overview
                </a>
                <span className="text-white hidden md:inline">|</span>
              </>
            )}

            {/* Add separator if there are content sections */}
            {contentSections.length > 0 && (
              <span className="text-white hidden md:inline">|</span>
            )}

            {/* Fixed sections */}
            <a
              href="#our-doctors"
              className="text-white hover:text-gray-200 transition-colors duration-300 text-sm md:text-base font-medium cursor-pointer no-underline"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Our Doctors
            </a>
            <span className="text-white hidden md:inline">|</span>
            <a
              href="#faq"
              className="text-white hover:text-gray-200 transition-colors duration-300 text-sm md:text-base font-medium cursor-pointer no-underline"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              FAQ
            </a>
          </nav>
        </div>
      </section>

      {/* Dynamic Content Sections */}
      {contentSections.length > 0 ? (
        contentSections.map((section, index) => (
          <section
            key={section.id}
            id={section.id}
            className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8"
            style={{ backgroundColor: index % 2 === 0 ? '#FDFDFD' : '#F3F4FA' }}
          >
            <div className="max-w-7xl mx-auto">
              <div className="max-w-4xl mx-auto">
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6"
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  {section.title}
                </h2>
                <div className="prose prose-lg max-w-none"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       lineHeight: '1.7'
                     }}>
                  <div
                    dangerouslySetInnerHTML={{ __html: section.content }}
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.7'
                    }}
                    className="[&>p]:mb-4 [&>ul]:mb-4 [&>ol]:mb-4 [&>li]:mb-2 [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h1]:text-[#E53274] [&>h2]:text-xl [&>h2]:font-bold [&>h2]:mb-3 [&>h2]:text-[#E53274] [&>h3]:text-lg [&>h3]:font-semibold [&>h3]:mb-2 [&>h3]:text-[#575C8D] [&>strong]:font-semibold [&>strong]:text-[#E53274]"
                  />
                </div>
              </div>
            </div>
          </section>
        ))
      ) : (
        // Fallback: Create default sections if no content sections are parsed
        <section
          id="overview"
          className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8"
          style={{ backgroundColor: '#FDFDFD' }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6"
                  style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                Overview
              </h2>
              <div className="prose prose-lg max-w-none"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     lineHeight: '1.7'
                   }}>
                <div
                  dangerouslySetInnerHTML={{ __html: service?.content?.rendered || '' }}
                  style={{
                    fontSize: '16px',
                    lineHeight: '1.7'
                  }}
                  className="[&>p]:mb-4 [&>ul]:mb-4 [&>ol]:mb-4 [&>li]:mb-2 [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h1]:text-[#E53274] [&>h2]:text-xl [&>h2]:font-bold [&>h2]:mb-3 [&>h2]:text-[#E53274] [&>h3]:text-lg [&>h3]:font-semibold [&>h3]:mb-2 [&>h3]:text-[#575C8D] [&>strong]:font-semibold [&>strong]:text-[#E53274]"
                />
              </div>
            </div>
          </div>
        </section>
      )}



      {/* Meet Your Dentist Section */}
      <section id="our-doctors" className="py-12 md:py-16 lg:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Meet Your Dentist
            </h2>
          </div>

          {/* Dentist Content */}
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center">
            {/* Left - Dentist Image */}
            <div className="flex justify-center order-1 md:order-1 lg:order-1 lg:justify-start">
              <Image
                src="/rockson.png"
                alt="Dr. Rockson Samuel - Dentist at Indira Dental Clinic"
                width={500}
                height={600}
                className="w-full max-w-sm md:max-w-md lg:max-w-lg h-auto object-contain"
                priority
              />
            </div>

            {/* Right - Dentist Info with Green Line */}
            <div className="relative order-2 md:order-2 lg:order-2 text-left md:text-center lg:text-left">
              {/* Green Vertical Line */}
              <div className="absolute left-0 top-0 bottom-0 w-1 hidden lg:block"
                   style={{ backgroundColor: '#93D214' }}>
              </div>

              {/* Content with Left Padding for Line */}
              <div className="lg:pl-8">
                <h3 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-6"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#E53274'
                    }}>
                  Meet<br />
                  Dr. Rockson<br />
                  Samuel
                </h3>

                <div className="mb-8">
                  <p className="text-base md:text-lg lg:text-xl mb-2"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    BDS, MDS – Implantologist
                  </p>
                  <p className="text-base md:text-lg lg:text-xl mb-2"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    Invisalign-certified
                  </p>
                  <p className="text-base md:text-lg lg:text-xl"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    Languages: Tamil | English | Hindi
                  </p>
                </div>

                {/* Call Back Button */}
                <div className="flex justify-center md:justify-center lg:justify-start">
                  <CallBackButton>
                    Get A Call Back
                  </CallBackButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Frequently Asked Questions
            </h2>
          </div>

          {/* FAQ Items */}
          <div className="max-w-4xl mx-auto space-y-4">
            {service.acf && (() => {
              const faqItems = [];
              for (let i = 1; i <= 10; i++) {
                const question = service.acf[`question_${i}`];
                const answer = service.acf[`answer_${i}`];
                if (question && answer) {
                  faqItems.push({ question, answer });
                }
              }
              return faqItems;
            })().length > 0 ? (
              (() => {
                const faqItems = [];
                for (let i = 1; i <= 10; i++) {
                  const question = service.acf[`question_${i}`];
                  const answer = service.acf[`answer_${i}`];
                  if (question && answer) {
                    faqItems.push({ question, answer });
                  }
                }
                return faqItems;
              })().map((faqItem: any, index: number) => (
                <div key={index}
                     className="border-2 rounded-lg overflow-hidden transition-all duration-300 hover:shadow-lg"
                     style={{
                       borderColor: '#93D214',
                       boxShadow: '5px 5px 4px 0px #00000040'
                     }}>
                  <button
                    className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none"
                    style={{ backgroundColor: '#FDFDFD' }}
                    onClick={() => toggleFAQ(index)}
                  >
                    <h3 className="text-lg md:text-xl font-semibold pr-4"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#2b2b2b'
                        }}>
                      {faqItem.question}
                    </h3>
                    <svg
                      className={`w-6 h-6 transition-transform duration-300 ${openFAQ === index ? 'rotate-180' : ''}`}
                      style={{ color: '#E53274' }}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {openFAQ === index && (
                    <div className="px-6 pb-4 border-t"
                         style={{
                           backgroundColor: '#FDFDFD',
                           borderColor: '#93D214'
                         }}>
                      <p className="text-base md:text-lg leading-relaxed pt-4"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           color: '#2b2b2b',
                           fontWeight: '400'
                         }}>
                        {faqItem.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))
            ) : (
              // Default FAQ if no ACF data
              <div className="text-center py-8">
                <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  No frequently asked questions available for this service.
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
      </div>
    </>
  );
}
