'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import CallBackForm from '@/components/ui/CallBackForm';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';
import { ServicePost } from '@/types/services';
import { fetchServiceBySlug } from '@/utils/services-api';

export default function ServicePage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [service, setService] = useState<ServicePost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // FAQ state management
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [contentSections, setContentSections] = useState<Array<{id: string, title: string, content: string}>>([]);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // Parse content sections from service content - Focus on H2 headings
  const parseContentSections = (htmlContent: string) => {
    const sections: Array<{id: string, title: string, content: string}> = [];

    // Split content by H2 tags specifically since that's what comes from REST API
    const h2Regex = /<h2[^>]*>(.*?)<\/h2>/gi;
    const parts = htmlContent.split(h2Regex);

    // Process each H2 heading and its following content
    for (let i = 1; i < parts.length; i += 2) {
      const title = parts[i].trim().replace(/<[^>]*>/g, ''); // Remove HTML tags from title
      const content = parts[i + 1] ? parts[i + 1].trim() : '';

      if (title && title.length > 2) { // Ensure meaningful titles
        // Create clean ID for CSS anchor navigation
        const id = title.toLowerCase()
          .replace(/[^a-z0-9\s]/g, '') // Remove special characters
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
          .substring(0, 50); // Limit ID length

        sections.push({
          id,
          title,
          content: content || '' // Content can be empty if it's just a heading
        });
      }
    }

    // If no H2 sections found, create a single overview section
    if (sections.length === 0 && htmlContent.trim()) {
      sections.push({
        id: 'overview',
        title: 'Overview',
        content: htmlContent
      });
    }

    return sections;
  };

  // Scroll to hero form function
  const scrollToHeroForm = () => {
    const heroSection = document.getElementById('hero-form');
    if (heroSection) {
      heroSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };



  // Consistent Call Back Button Component
  const CallBackButton = ({ children, className = "", onClick }: {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
  }) => (
    <button
      className={`bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold hover:shadow-lg hover:scale-105 border-2 border-white ${className}`}
      style={{ fontFamily: 'DM Sans, sans-serif' }}
      onClick={onClick || scrollToHeroForm}
    >
      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
      </svg>
      {children}
    </button>
  );

  // Fetch service data
  useEffect(() => {
    const fetchServiceData = async () => {
      try {
        setLoading(true);
        setError(null);
        const serviceData = await fetchServiceBySlug(slug);
        if (serviceData) {
          setService(serviceData);
          // Parse content sections
          const sections = parseContentSections(serviceData.content.rendered);
          setContentSections(sections);
        } else {
          setError('Service not found');
        }
      } catch (error) {
        console.error('Error fetching service data:', error);
        setError('Failed to load service');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchServiceData();
    }
  }, [slug]);

  // Helper function to extract text from HTML content
  const extractTextFromHtml = (html: string): string => {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#039;/g, "'") // Replace &#039; with '
      .trim();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#E53274] mx-auto mb-4"></div>
          <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Loading service details...
          </p>
        </div>
      </div>
    );
  }

  if (error || !service) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Service Not Found
          </h1>
          <p className="text-lg mb-8" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            {error || 'The requested service could not be found.'}
          </p>
          <Link href="/services" 
                className="inline-block bg-[#E53274] hover:bg-[#C7205D] text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300"
                style={{ fontFamily: 'Inter, sans-serif' }}>
            View All Services
          </Link>
        </div>
      </div>
    );
  }

  const excerpt = extractTextFromHtml(service.excerpt.rendered) || 
                 extractTextFromHtml(service.content.rendered).substring(0, 200) + '...';

  return (
    <>
      {/* CSS for smooth scrolling and offset for sticky headers */}
      <style jsx>{`
        html {
          scroll-behavior: smooth;
        }

        /* Offset for sticky headers when using anchor links */
        section[id] {
          scroll-margin-top: 170px; /* Main header (90px) + Service nav (60px) + buffer (20px) */
        }

        /* Remove underline from navigation links */
        .no-underline {
          text-decoration: none !important;
        }

        .no-underline:hover {
          text-decoration: none !important;
        }
      `}</style>

      <div className="min-h-screen service-page">
      {/* Hero Section - Blog Style Header */}
      <section
        className="relative py-8 md:py-12 lg:py-16 px-4 sm:px-6 lg:px-8"
        style={{ backgroundColor: '#FDFDFD' }}
      >
        <div className="max-w-4xl mx-auto">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <div className="flex items-center space-x-2 text-sm"
                 style={{ fontFamily: 'Inter', color: '#575C8D' }}>
              <Link href="/" className="hover:text-[#E53274] transition-colors">
                Home
              </Link>
              <span>/</span>
              <Link href="/services" className="hover:text-[#E53274] transition-colors">
                Services
              </Link>
              <span>/</span>
              <span style={{ color: '#E53274' }}>{service.title.rendered}</span>
            </div>
          </nav>

          {/* Article Header */}
          <header className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4"
                style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#E53274' }}>
              {service.title.rendered} in Vellore
            </h1>

            <p className="text-lg md:text-xl mb-6 max-w-3xl mx-auto"
               style={{ fontFamily: 'Inter', fontWeight: 400, color: '#2b2b2b', lineHeight: '1.6' }}>
              {excerpt}
            </p>

            {/* Meta Information */}
            <div className="flex flex-wrap justify-center items-center gap-4 mb-8 text-sm"
                 style={{ fontFamily: 'Inter', color: '#575C8D' }}>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span>Updated: {new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <span>5 min read</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <BookAppointmentButton size="lg" className="w-full sm:w-auto">
                Book Appointment
              </BookAppointmentButton>
              <CallBackButton className="w-full sm:w-auto">
                Get A Call Back
              </CallBackButton>
            </div>
          </header>

          {/* Featured Media */}
          <div className="mb-8">
            <div className="relative w-full rounded-lg overflow-hidden shadow-lg" style={{ paddingBottom: '56.25%' }}>
              <iframe
                className="absolute top-0 left-0 w-full h-full"
                src="https://www.youtube.com/embed/cR4f7FXuSfc"
                title="Dental Treatment Video"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Benefits Section */}
      <section className="py-8 md:py-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Benefit 1 */}
            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[#E53274] rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold mb-2" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
                Expert Care
              </h3>
              <p className="text-sm" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
                Advanced techniques with gentle approach
              </p>
            </div>

            {/* Benefit 2 */}
            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[#E53274] rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                    <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold mb-2" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
                Transparent Pricing
              </h3>
              <p className="text-sm" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
                Clear costs with no hidden charges
              </p>
            </div>

            {/* Benefit 3 */}
            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[#E53274] rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold mb-2" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
                Comfort First
              </h3>
              <p className="text-sm" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
                Pain-free treatments with modern technology
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Table of Contents - Sticky Navigation */}
      <section className="sticky top-[90px] z-[9998] py-4 px-4 sm:px-6 lg:px-8 border-b"
               style={{ backgroundColor: '#FDFDFD', borderColor: '#E5E7EB' }}>
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
              Table of Contents
            </h3>
            <nav className="hidden md:flex items-center gap-6">
              {/* Dynamic content sections from H2 headings */}
              {contentSections.length > 0 ? (
                contentSections.map((section, index) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="text-[#575C8D] hover:text-[#E53274] transition-colors duration-300 text-sm font-medium cursor-pointer no-underline"
                    style={{ fontFamily: 'Inter, sans-serif' }}
                  >
                    {section.title}
                  </a>
                ))
              ) : (
                <a
                  href="#overview"
                  className="text-[#575C8D] hover:text-[#E53274] transition-colors duration-300 text-sm font-medium cursor-pointer no-underline"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Overview
                </a>
              )}

              <a
                href="#our-doctors"
                className="text-[#575C8D] hover:text-[#E53274] transition-colors duration-300 text-sm font-medium cursor-pointer no-underline"
                style={{ fontFamily: 'Inter, sans-serif' }}
              >
                Our Doctors
              </a>
              <a
                href="#faq"
                className="text-[#575C8D] hover:text-[#E53274] transition-colors duration-300 text-sm font-medium cursor-pointer no-underline"
                style={{ fontFamily: 'Inter, sans-serif' }}
              >
                FAQ
              </a>
            </nav>

            {/* Mobile dropdown toggle */}
            <button className="md:hidden text-[#E53274]">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* Main Content Area */}
      <div className="py-8 md:py-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <article className="prose prose-lg max-w-none">
                {/* Dynamic Content Sections */}
                {contentSections.length > 0 ? (
                  contentSections.map((section, index) => (
                    <section key={section.id} id={section.id} className="mb-12">
                      <h2 className="text-2xl md:text-3xl font-bold mb-6 border-b-2 border-[#E53274] pb-3"
                          style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                        {section.title}
                      </h2>
                      <div className="content-section"
                           style={{
                             fontFamily: 'Inter, sans-serif',
                             color: '#2b2b2b',
                             lineHeight: '1.7',
                             fontSize: '16px'
                           }}>
                        <div
                          dangerouslySetInnerHTML={{ __html: section.content }}
                          className="[&>p]:mb-4 [&>ul]:mb-4 [&>ol]:mb-4 [&>li]:mb-2 [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h1]:text-[#E53274] [&>h2]:text-xl [&>h2]:font-bold [&>h2]:mb-3 [&>h2]:text-[#E53274] [&>h3]:text-lg [&>h3]:font-semibold [&>h3]:mb-2 [&>h3]:text-[#575C8D] [&>strong]:font-semibold [&>strong]:text-[#E53274] [&>ul]:list-disc [&>ul]:pl-6 [&>ol]:list-decimal [&>ol]:pl-6"
                        />
                      </div>
                    </section>
                  ))
                ) : (
                  // Fallback content
                  <section id="overview" className="mb-12">
                    <h2 className="text-2xl md:text-3xl font-bold mb-6 border-b-2 border-[#E53274] pb-3"
                        style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                      Overview
                    </h2>
                    <div className="content-section"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           color: '#2b2b2b',
                           lineHeight: '1.7',
                           fontSize: '16px'
                         }}>
                      <div
                        dangerouslySetInnerHTML={{ __html: service?.content?.rendered || '' }}
                        className="[&>p]:mb-4 [&>ul]:mb-4 [&>ol]:mb-4 [&>li]:mb-2 [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h1]:text-[#E53274] [&>h2]:text-xl [&>h2]:font-bold [&>h2]:mb-3 [&>h2]:text-[#E53274] [&>h3]:text-lg [&>h3]:font-semibold [&>h3]:mb-2 [&>h3]:text-[#575C8D] [&>strong]:font-semibold [&>strong]:text-[#E53274] [&>ul]:list-disc [&>ul]:pl-6 [&>ol]:list-decimal [&>ol]:pl-6"
                      />
                    </div>
                  </section>
                )}
              </article>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-[200px]">
                {/* Contact Form Widget */}
                <div className="bg-[#F3F4FA] rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
                    Get A Call Back
                  </h3>
                  <p className="text-sm mb-4" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
                    Have questions? Our dental experts are here to help.
                  </p>
                  <CallBackButton className="w-full text-center">
                    Request Call Back
                  </CallBackButton>
                </div>

                {/* Quick Info */}
                <div className="bg-white rounded-lg p-6 border border-gray-200">
                  <h3 className="text-lg font-bold mb-4" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
                    Quick Info
                  </h3>
                  <div className="space-y-3 text-sm" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-[#E53274]" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <span>Treatment Duration: 30-60 mins</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-[#E53274]" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                        <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                      </svg>
                      <span>Flexible Payment Options</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-[#E53274]" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Advanced Technology</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Meet Your Dentist Section */}
      <section id="our-doctors" className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 border-b-2 border-[#E53274] pb-3 inline-block"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Meet Your Dentist
            </h2>
          </div>

          {/* Dentist Card */}
          <div className="bg-white rounded-lg shadow-lg p-6 md:p-8">
            <div className="grid md:grid-cols-2 gap-6 items-center">
              {/* Dentist Image */}
              <div className="text-center">
                <Image
                  src="/rockson.png"
                  alt="Dr. Rockson Samuel - Dentist at Indira Dental Clinic"
                  width={300}
                  height={360}
                  className="w-full max-w-xs mx-auto h-auto object-contain rounded-lg"
                  priority
                />
              </div>

              {/* Dentist Info */}
              <div className="text-center md:text-left">
                <h3 className="text-2xl md:text-3xl font-bold mb-4"
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Dr. Rockson Samuel
                </h3>

                <div className="mb-6 space-y-2">
                  <p className="text-lg font-medium"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                    BDS, MDS – Implantologist
                  </p>
                  <p className="text-base"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#575C8D' }}>
                    Invisalign-certified
                  </p>
                  <p className="text-base"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#575C8D' }}>
                    Languages: Tamil | English | Hindi
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <BookAppointmentButton size="md" className="w-full sm:w-auto">
                    Book Appointment
                  </BookAppointmentButton>
                  <CallBackButton className="w-full sm:w-auto">
                    Get A Call Back
                  </CallBackButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 border-b-2 border-[#E53274] pb-3 inline-block"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Frequently Asked Questions
            </h2>
          </div>

          {/* FAQ Items */}
          <div className="space-y-4">
            {service.acf && (() => {
              const faqItems = [];
              for (let i = 1; i <= 10; i++) {
                const question = service.acf[`question_${i}`];
                const answer = service.acf[`answer_${i}`];
                if (question && answer) {
                  faqItems.push({ question, answer });
                }
              }
              return faqItems;
            })().length > 0 ? (
              (() => {
                const faqItems = [];
                for (let i = 1; i <= 10; i++) {
                  const question = service.acf[`question_${i}`];
                  const answer = service.acf[`answer_${i}`];
                  if (question && answer) {
                    faqItems.push({ question, answer });
                  }
                }
                return faqItems;
              })().map((faqItem: any, index: number) => (
                <div key={index}
                     className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                  <button
                    className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none hover:bg-gray-50"
                    onClick={() => toggleFAQ(index)}
                  >
                    <h3 className="text-lg font-semibold pr-4"
                        style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                      {faqItem.question}
                    </h3>
                    <svg
                      className={`w-5 h-5 transition-transform duration-300 ${openFAQ === index ? 'rotate-180' : ''}`}
                      style={{ color: '#E53274' }}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {openFAQ === index && (
                    <div className="px-6 pb-4 border-t border-gray-200 bg-gray-50">
                      <p className="text-base leading-relaxed pt-4"
                         style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                        {faqItem.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))
            ) : (
              // Default FAQ if no ACF data
              <div className="text-center py-8">
                <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  No frequently asked questions available for this service.
                </p>
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12 p-8 bg-[#F3F4FA] rounded-lg">
            <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans', color: '#E53274' }}>
              Still Have Questions?
            </h3>
            <p className="text-base mb-6" style={{ fontFamily: 'Inter', color: '#2b2b2b' }}>
              Our dental experts are here to help you understand your treatment options.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <BookAppointmentButton size="lg" className="w-full sm:w-auto">
                Book Consultation
              </BookAppointmentButton>
              <CallBackButton className="w-full sm:w-auto">
                Get A Call Back
              </CallBackButton>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
      </div>
    </>
  );
}
