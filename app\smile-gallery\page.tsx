'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { SmileGalleryCardData } from '@/types/smile-gallery';
import { getAllSmileGallery } from '@/utils/smile-gallery-api';
import SmileGalleryCard from '@/components/ui/SmileGalleryCard';

export default function SmileGalleryPage() {
  const [galleryData, setGalleryData] = useState<SmileGalleryCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGalleryData = async () => {
      try {
        setLoading(true);
        const data = await getAllSmileGallery();
        setGalleryData(data);
      } catch (err) {
        setError('Failed to load smile gallery. Please try again later.');
        console.error('Error fetching gallery data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchGalleryData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274] mb-4"></div>
          <p className="text-lg font-semibold text-[#E53274]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Loading Smile Gallery...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <p className="text-lg font-semibold text-red-600 mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-[#E53274] hover:bg-[#C7205D] text-white px-6 py-3 rounded-lg transition-all duration-300"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
      <div className="max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4"
              style={{
                fontFamily: 'DM Sans, sans-serif',
                color: '#E53274'
              }}>
            Smile Gallery
          </h1>
          <p className="text-lg md:text-xl max-w-4xl mx-auto"
             style={{
               fontFamily: 'Inter, sans-serif',
               color: '#2b2b2b',
               fontWeight: '400'
             }}>
            Discover the amazing transformations our patients have achieved. Each smile tells a story of confidence, 
            health, and happiness restored through our expert dental care.
          </p>
        </div>

        {/* Gallery Grid */}
        {galleryData.length > 0 ? (
          <div className="flex flex-wrap justify-center gap-8">
            {galleryData.map((item) => (
              <SmileGalleryCard key={item.id} data={item} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <p className="text-lg text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
              No smile gallery posts found. Check back soon for amazing transformations!
            </p>
          </div>
        )}

        {/* Back to Home Button */}
        <div className="text-center mt-12">
          <Link
            href="/"
            className="bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold hover:shadow-lg hover:scale-105"
            style={{ fontFamily: 'DM Sans, sans-serif' }}
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
