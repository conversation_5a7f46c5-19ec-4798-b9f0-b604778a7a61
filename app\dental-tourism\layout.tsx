import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Dental Tourism in Vellore - Save 60-80% on World-Class Dental Care | Indira Dental Clinic",
  description: "Experience premium dental tourism in Vellore, India. Save 60-80% on dental implants, cosmetic dentistry, and orthodontics with international-standard care at Indira Dental Clinic.",
  keywords: "dental tourism India, dental tourism Vellore, cheap dental implants India, dental vacation India, international dental care, affordable dentistry India",
  openGraph: {
    title: "Dental Tourism in Vellore - Save 60-80% on World-Class Dental Care",
    description: "Experience premium dental tourism in Vellore, India. Save 60-80% on dental implants, cosmetic dentistry, and orthodontics with international-standard care.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Dental Tourism in Vellore - Save 60-80% on World-Class Dental Care",
    description: "Experience premium dental tourism in Vellore, India. Save 60-80% on dental implants, cosmetic dentistry, and orthodontics.",
  },
};

export default function DentalTourismLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
