import { <PERSON>logPost, BlogCategory, BlogAuthor, FeaturedMediaResponse, BlogCardData, BlogPostData } from '@/types/blog';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Cache for API responses to improve performance
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper function to get cached data or fetch new data
async function getCachedData<T>(key: string, fetchFn: () => Promise<T>): Promise<T> {
  const cached = cache.get(key);
  const now = Date.now();

  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  try {
    const data = await fetchFn();
    cache.set(key, { data, timestamp: now });
    return data;
  } catch (error) {
    // Return cached data if available, even if expired
    if (cached) {
      console.warn(`Using expired cache for ${key} due to fetch error:`, error);
      return cached.data;
    }
    throw error;
  }
}

// Helper function to extract text from HTML
function extractTextFromHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').trim();
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Fetch blog posts from WordPress API with caching
export async function fetchBlogPosts(): Promise<BlogPost[]> {
  return getCachedData('blog-posts', async () => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // Reduced to 5 seconds

    try {
      const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/posts?per_page=20&_embed=author`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        next: { revalidate: 300 } // Cache for 5 minutes
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch blog posts: ${response.status} ${response.statusText}`);
      }

      const posts: BlogPost[] = await response.json();
      return Array.isArray(posts) ? posts : [];
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('Error fetching blog posts:', error);
      return [];
    }
  });
}

// Fetch single blog post by slug
export async function fetchBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/posts?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch blog post: ${response.status} ${response.statusText}`);
    }

    const posts: BlogPost[] = await response.json();
    return posts.length > 0 ? posts[0] : null;
  } catch (error) {
    console.error('Error fetching blog post by slug:', error);
    return null;
  }
}

// Fetch categories with caching
export async function fetchCategories(): Promise<BlogCategory[]> {
  return getCachedData('categories', async () => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // Reduced timeout

    try {
      const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/categories?per_page=50`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        next: { revalidate: 600 } // Cache for 10 minutes
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
      }

      const categories: BlogCategory[] = await response.json();
      return Array.isArray(categories) ? categories : [];
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('Error fetching categories:', error);
      return [];
    }
  });
}

// Fetch authors with caching
export async function fetchAuthors(): Promise<BlogAuthor[]> {
  return getCachedData('authors', async () => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // Reduced timeout

    try {
      const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/users?per_page=50`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        next: { revalidate: 600 } // Cache for 10 minutes
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch authors: ${response.status} ${response.statusText}`);
      }

      const authors: BlogAuthor[] = await response.json();
      return Array.isArray(authors) ? authors : [];
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('Error fetching authors:', error);
      return [];
    }
  });
}

// Fetch single author by ID
export async function fetchAuthorById(authorId: number): Promise<BlogAuthor | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/users/${authorId}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch author: ${response.status} ${response.statusText}`);
    }

    const author: BlogAuthor = await response.json();
    return author;
  } catch (error) {
    console.error('Error fetching author by ID:', error);
    return null;
  }
}

// Fetch tags
export async function fetchTags(): Promise<BlogCategory[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/tags`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch tags: ${response.status} ${response.statusText}`);
    }

    const tags: BlogCategory[] = await response.json();
    return tags;
  } catch (error) {
    console.error('Error fetching tags:', error);
    return [];
  }
}

// Fetch featured media
export async function fetchFeaturedMedia(mediaId: number): Promise<string> {
  if (!mediaId) return '';

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/media/${mediaId}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return '';
    }

    const media: FeaturedMediaResponse = await response.json();
    return media.source_url || '';
  } catch (error) {
    console.error('Error fetching featured media:', error);
    return '';
  }
}

// Process blog posts data for display with optimized parallel processing
async function processBlogData(posts: BlogPost[]): Promise<BlogCardData[]> {
  if (!posts || posts.length === 0) return [];

  try {
    // Fetch all required data in parallel for better performance
    const [categories, allAuthors] = await Promise.all([
      fetchCategories(),
      fetchAuthors() // Fetch all authors at once instead of individual calls
    ]);

    // Get unique author IDs and featured media IDs for batch processing
    const uniqueAuthorIds = [...new Set(posts.map(post => post.author))];
    const uniqueMediaIds = [...new Set(posts.map(post => post.featured_media).filter(id => id > 0))];

    // Fetch all featured images in parallel
    const mediaPromises = uniqueMediaIds.map(id =>
      fetchFeaturedMedia(id).catch(error => {
        console.warn(`Failed to fetch media ${id}:`, error);
        return '';
      })
    );
    const featuredImages = await Promise.all(mediaPromises);
    const mediaMap = new Map(uniqueMediaIds.map((id, index) => [id, featuredImages[index]]));

    // Process posts with cached data
    const processedData: BlogCardData[] = [];

    for (const post of posts) {
      try {
        // Validate post data
        if (!post.id || !post.title?.rendered || !post.slug) {
          console.warn('Invalid post data:', post);
          continue;
        }

        // Extract excerpt from excerpt field or content
        const excerpt = extractTextFromHtml(post.excerpt?.rendered || '') ||
                       extractTextFromHtml(post.content?.rendered || '').substring(0, 150) + '...';

        // Get author data from cached authors
        const authorData = allAuthors.find(author => author.id === post.author);
        const authorName = authorData?.name || 'Unknown Author';

        // Get category names
        const postCategories = categories.filter(cat => post.categories?.includes(cat.id));
        const categoryNames = postCategories.map(cat => cat.name);

        // Get featured image from cache
        const featuredImage = mediaMap.get(post.featured_media) || '';

        processedData.push({
          id: post.id,
          title: post.title.rendered,
          excerpt,
          slug: post.slug,
          date: formatDate(post.date),
          author: authorName,
          authorId: post.author,
          authorData: authorData || undefined,
          featuredImage,
          categories: categoryNames,
          categoryIds: post.categories || []
        });
      } catch (error) {
        console.error(`Error processing blog post ${post.id}:`, error);
      }
    }

    return processedData;
  } catch (error) {
    console.error('Error in processBlogData:', error);
    return [];
  }
}

// Get all blog posts data
export async function getAllBlogPosts(): Promise<BlogCardData[]> {
  const posts = await fetchBlogPosts();
  return await processBlogData(posts);
}

// Get the most recent author for blog listing page
export async function getMostRecentAuthor(): Promise<BlogAuthor | null> {
  try {
    const posts = await fetchBlogPosts();
    if (posts.length === 0) return null;

    // Get the most recent post's author
    const mostRecentPost = posts[0]; // Posts are usually ordered by date
    return await fetchAuthorById(mostRecentPost.author);
  } catch (error) {
    console.error('Error fetching most recent author:', error);
    return null;
  }
}

// Get blog post data for single post page with optimized data fetching
export async function getBlogPostData(slug: string): Promise<BlogPostData | null> {
  if (!slug) return null;

  try {
    const post = await fetchBlogPostBySlug(slug);
    if (!post || !post.id || !post.title?.rendered) {
      console.warn('Invalid or missing post data for slug:', slug);
      return null;
    }

    // Fetch all required data in parallel for better performance
    const [categories, tags, authorData, featuredImage] = await Promise.all([
      fetchCategories().catch(error => {
        console.warn('Failed to fetch categories:', error);
        return [];
      }),
      fetchTags().catch(error => {
        console.warn('Failed to fetch tags:', error);
        return [];
      }),
      fetchAuthorById(post.author).catch(error => {
        console.warn(`Failed to fetch author ${post.author}:`, error);
        return null;
      }),
      fetchFeaturedMedia(post.featured_media).catch(error => {
        console.warn(`Failed to fetch featured media ${post.featured_media}:`, error);
        return '';
      })
    ]);

    // Extract excerpt from excerpt field or content
    const excerpt = extractTextFromHtml(post.excerpt?.rendered || '') ||
                   extractTextFromHtml(post.content?.rendered || '').substring(0, 150) + '...';

    // Get author name with validation
    const authorName = authorData?.name || 'Unknown Author';

    // Get category names with validation
    const postCategories = categories.filter(cat =>
      cat && cat.id && post.categories?.includes(cat.id)
    );
    const categoryNames = postCategories.map(cat => cat.name);

    // Get tag names with validation
    const postTags = tags.filter(tag =>
      tag && tag.id && post.tags?.includes(tag.id)
    );
    const tagNames = postTags.map(tag => tag.name);

    return {
      id: post.id,
      title: post.title.rendered,
      excerpt,
      slug: post.slug,
      date: formatDate(post.date),
      author: authorName,
      authorId: post.author,
      authorData: authorData || undefined,
      featuredImage,
      categories: categoryNames,
      categoryIds: post.categories || [],
      content: post.content?.rendered || '',
      tags: tagNames,
      tagIds: post.tags || []
    };
  } catch (error) {
    console.error(`Error fetching blog post data for slug "${slug}":`, error);
    return null;
  }
}
