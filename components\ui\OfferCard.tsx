'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { OfferCardData } from '@/types/offers';
import { fetchFeaturedMedia, formatOfferDate, isOfferActive } from '@/utils/offers-api';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';

interface OfferCardProps {
  offer: OfferCardData;
}

export default function OfferCard({ offer }: OfferCardProps) {
  const [featuredImage, setFeaturedImage] = useState<string>('');
  const [imageLoading, setImageLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      if (offer.featured_media) {
        try {
          const imageUrl = await fetchFeaturedMedia(offer.featured_media);
          setFeaturedImage(imageUrl);
        } catch (error) {
          console.error('Error loading offer image:', error);
        }
      }
      setImageLoading(false);
    };

    loadImage();
  }, [offer.featured_media]);

  const isActive = offer.start_date && offer.end_date ? isOfferActive(offer.start_date, offer.end_date) : false;
  const startDate = offer.start_date ? formatOfferDate(offer.start_date) : '';
  const endDate = offer.end_date ? formatOfferDate(offer.end_date) : '';

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Offer Image */}
      <div className="relative h-48 bg-gradient-to-br from-[#E53274] to-[#F97316]">
        {!imageLoading && featuredImage ? (
          <Image
            src={featuredImage}
            alt={offer.title}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            {imageLoading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            ) : (
              <div className="text-white text-center p-4">
                <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
                <p className="text-sm font-medium">Special Offer</p>
              </div>
            )}
          </div>
        )}
        
        {isActive && (
          <div className="absolute top-4 right-4 bg-[#93D214] text-white px-3 py-1 rounded-full text-sm font-semibold">
            Limited Time Offer
          </div>
        )}
      </div>

      {/* Offer Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-[#2b2b2b] mb-3" style={{ fontFamily: 'DM Sans, sans-serif' }}>
          {offer.title}
        </h3>

        <div className="text-[#2b2b2b] mb-4" style={{ fontFamily: 'Inter, sans-serif' }}>
          <div dangerouslySetInnerHTML={{ __html: offer.excerpt }} />
        </div>

        {/* Offer Dates */}
        {(startDate || endDate) && (
          <div className="mb-4 space-y-2">
            {startDate && (
              <div className="flex items-center gap-2 text-sm text-[#E53274]">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span>Start Date: {startDate}</span>
              </div>
            )}
            {endDate && (
              <div className="flex items-center gap-2 text-sm text-[#E53274]">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span>End Date: {endDate}</span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Link
            href={`/offers/${offer.slug}`}
            className="flex-1 bg-[#E53274] hover:bg-[#C7205D] text-white text-center py-2 px-4 rounded-md transition-colors text-sm font-medium"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            View Details
          </Link>
          <BookAppointmentButton size="sm" />
        </div>
      </div>
    </div>
  );
}
