'use client';

import Image from 'next/image';
import Link from 'next/link';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';

export default function DentistsPage() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#FDFDFD' }}>
      {/* Hero Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Meet Our Expert Dentists
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Our experienced team of dental professionals is dedicated to providing 
            exceptional care with the latest techniques and technologies.
          </p>
        </div>
      </section>

      {/* Main Dentist Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-16">
            {/* Dentist Image */}
            <div className="flex justify-center">
              <div className="relative">
                <Image
                  src="/rockson.png"
                  alt="Dr. Rockson - Lead Dentist"
                  width={400}
                  height={500}
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>

            {/* Dentist Info */}
            <div>
              <h2 className="text-2xl md:text-3xl font-bold mb-4" 
                  style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                Dr. Rockson
              </h2>
              <p className="text-lg mb-4" 
                 style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274', fontWeight: 600 }}>
                Lead Dentist & Clinic Director
              </p>
              
              <div className="space-y-4 text-base md:text-lg mb-6" 
                   style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                <p>
                  Dr. Rockson brings years of experience in comprehensive dental care, 
                  specializing in advanced restorative and cosmetic dentistry procedures.
                </p>
                <p>
                  With a commitment to patient comfort and cutting-edge techniques, 
                  Dr. Rockson ensures every patient receives personalized, high-quality care.
                </p>
              </div>

              {/* Specializations */}
              <div className="mb-6">
                <h3 className="text-lg font-bold mb-3" 
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                  Specializations:
                </h3>
                <ul className="space-y-2" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  <li>• Root Canal Treatment</li>
                  <li>• Cosmetic Dentistry</li>
                  <li>• Dental Implants</li>
                  <li>• Orthodontics</li>
                  <li>• Preventive Care</li>
                </ul>
              </div>

              {/* Green line separator */}
              <div className="w-16 h-1 mb-6" style={{ backgroundColor: '#93D214' }}></div>

              {/* CTA Button */}
              <BookAppointmentButton>
                Book Consultation
              </BookAppointmentButton>
            </div>
          </div>
        </div>
      </section>

      {/* Team Values Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-12" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Why Choose Our Team?
          </h2>
          <div className="grid md:grid-cols-3 gap-6 md:gap-8">
            {[
              {
                title: "Expert Experience",
                description: "Years of specialized training and hands-on experience in all areas of dentistry.",
                icon: "🎓"
              },
              {
                title: "Patient-Centered Care",
                description: "We listen to your concerns and tailor treatments to your specific needs.",
                icon: "❤️"
              },
              {
                title: "Advanced Technology",
                description: "State-of-the-art equipment and modern techniques for optimal results.",
                icon: "🔬"
              }
            ].map((feature, index) => (
              <div key={index} className="text-center">
                <div className="text-5xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold mb-3" 
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                  {feature.title}
                </h3>
                <p style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F1F2F8' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Ready to Meet Our Team?
          </h2>
          <p className="text-lg mb-8" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Schedule your appointment today and experience personalized dental care from our expert team.
          </p>
          <BookAppointmentButton size="lg">
            Book Your Appointment
          </BookAppointmentButton>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
