'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { BlogCardData, BlogAuthor } from '@/types/blog';
import { getAllBlogPosts, getMostRecentAuthor } from '@/utils/blog-api';
import BlogCard from '@/components/ui/blog/BlogCard';
import AuthorProfile from '@/components/ui/blog/AuthorProfile';
import Categories from '@/components/ui/blog/Categories';
import ContactInfoSection from '@/components/ui/ContactInfoSection';

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogCardData[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [primaryAuthor, setPrimaryAuthor] = useState<BlogCardData['authorData'] | undefined>(undefined);
  const searchParams = useSearchParams();
  const categoryFilter = searchParams.get('category');

  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Add timeout for the entire operation
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), 10000)
        );

        const dataPromise = Promise.all([
          getAllBlogPosts(),
          getMostRecentAuthor()
        ]);

        const [blogPosts, recentAuthor] = await Promise.race([
          dataPromise,
          timeoutPromise
        ]) as [BlogCardData[], any];

        // Validate data
        if (!Array.isArray(blogPosts)) {
          throw new Error('Invalid blog posts data received');
        }

        setPosts(blogPosts);

        // Apply category filter if present
        if (categoryFilter) {
          const filtered = blogPosts.filter(post =>
            post.categories?.some(cat =>
              cat.toLowerCase().replace(/\s+/g, '-') === categoryFilter ||
              cat.toLowerCase() === categoryFilter
            )
          );
          setFilteredPosts(filtered);
        } else {
          setFilteredPosts(blogPosts);
        }

        // Set primary author from API or from first post with author data
        if (recentAuthor) {
          setPrimaryAuthor(recentAuthor);
        } else {
          const postWithAuthor = blogPosts.find(post => post.authorData);
          if (postWithAuthor && postWithAuthor.authorData) {
            setPrimaryAuthor(postWithAuthor.authorData);
          }
        }
      } catch (err) {
        console.error('Error loading blog posts:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load blog posts';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadPosts();
  }, [categoryFilter]);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8"
               style={{
                 background: 'linear-gradient(135deg, #E53274 0%, #575C8D 100%)'
               }}>
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6"
              style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {categoryFilter ? `Blog - ${categoryFilter.charAt(0).toUpperCase() + categoryFilter.slice(1).replace(/-/g, ' ')}` : 'Our Blog'}
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto"
             style={{ fontFamily: 'Inter, sans-serif' }}>
            {categoryFilter
              ? `Browse our ${categoryFilter.replace(/-/g, ' ')} related articles and insights`
              : 'Stay updated with the latest dental health tips, treatments, and news from our expert team'
            }
          </p>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Blog Posts - Main Content */}
            <div className="lg:col-span-3">
              {loading && (
                <div className="text-center py-12">
                  <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274]"></div>
                  <p className="mt-4 text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                    Loading blog posts...
                  </p>
                  <p className="mt-2 text-sm text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                    This may take a few moments
                  </p>
                </div>
              )}

              {error && (
                <div className="text-center py-12">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                      <i className="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <h3 className="text-lg font-semibold text-red-800 mb-2" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Unable to Load Blog Posts
                    </h3>
                    <p className="text-red-600 mb-4" style={{ fontFamily: 'Inter, sans-serif' }}>
                      {error}
                    </p>
                    <button
                      onClick={() => window.location.reload()}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      style={{ fontFamily: 'Inter, sans-serif' }}
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              )}

              {!loading && !error && filteredPosts.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                    {categoryFilter ? `No blog posts found for "${categoryFilter}".` : 'No blog posts found.'}
                  </p>
                </div>
              )}

              {!loading && !error && filteredPosts.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {filteredPosts.map((post) => (
                    <BlogCard key={post.id} post={post} />
                  ))}
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-8">
              {/* Author Profile */}
              <AuthorProfile authorData={primaryAuthor} />

              {/* Categories */}
              <Categories />
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <ContactInfoSection />
    </div>
  );
}
