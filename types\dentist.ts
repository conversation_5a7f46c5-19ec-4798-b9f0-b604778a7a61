// WordPress dentist post structure
export interface DentistPost {
  id: number;
  date: string;
  date_gmt: string;
  guid: {
    rendered: string;
  };
  modified: string;
  modified_gmt: string;
  slug: string;
  status: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    protected: boolean;
  };
  excerpt: {
    rendered: string;
    protected: boolean;
  };
  featured_media: number;
  template: string;
  meta: {
    _acf_changed: boolean;
  };
  speciality: number[];
  class_list: string[];
  acf?: DentistACF;
  _links: any;
}

// ACF fields for dentist
export interface DentistACF {
  clinic_location?: {
    address: string;
    lat: number;
    lng: number;
    zoom: number;
    place_id: string;
    name: string;
    street_number: string;
    street_name: string;
    street_name_short: string;
    city: string;
    state: string;
    state_short: string;
    post_code: number;
    country: string;
    country_short: string;
  };
  dentist_image_1?: number;
  dentist_image_2?: number | string;
  dentist_image_3?: number | string;
  dentist_image_4?: number | string;
  dentist_image_5?: number | string;

  // Professional Information
  qualification?: string;
  experience_years?: string | number;
  designation?: string;
  registration_number?: string;
  education?: string;
  certifications?: string;

  // Contact Information
  phone_number?: string;
  email?: string;
  consultation_hours?: string;

  // Professional Details
  languages_spoken?: string;
  areas_of_expertise?: string;
  awards_achievements?: string;
  professional_memberships?: string;

  // Additional Information
  bio?: string;
  motto?: string;
  consultation_fee?: string | number;
  availability?: string;

  // Social Media & Online Presence
  website?: string;
  linkedin?: string;
  facebook?: string;
  instagram?: string;

  // Practice Information
  years_of_practice?: string | number;
  patients_treated?: string | number;
  success_rate?: string | number;

  // Services & Treatments
  treatments_offered?: string;
  special_procedures?: string;
  equipment_used?: string;
}

// Speciality data structure
export interface SpecialityData {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

// Processed dentist data for display
export interface DentistCardData {
  id: number;
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  featured_media: number;
  image_url?: string;
  specialities: number[];
  speciality_names?: string[];
  clinic_location?: {
    address: string;
    lat: number;
    lng: number;
    place_id: string;
    name: string;
    city: string;
    state: string;
  };
  dentist_images?: string[];

  // Professional Information
  qualification?: string;
  experience_years?: string | number;
  designation?: string;
  registration_number?: string;
  education?: string;
  certifications?: string;

  // Contact Information
  phone_number?: string;
  email?: string;
  consultation_hours?: string;

  // Professional Details
  languages_spoken?: string;
  areas_of_expertise?: string;
  awards_achievements?: string;
  professional_memberships?: string;

  // Additional Information
  bio?: string;
  motto?: string;
  consultation_fee?: string | number;
  availability?: string;

  // Social Media & Online Presence
  website?: string;
  linkedin?: string;
  facebook?: string;
  instagram?: string;

  // Practice Information
  years_of_practice?: string | number;
  patients_treated?: string | number;
  success_rate?: string | number;

  // Services & Treatments
  treatments_offered?: string;
  special_procedures?: string;
  equipment_used?: string;
}
