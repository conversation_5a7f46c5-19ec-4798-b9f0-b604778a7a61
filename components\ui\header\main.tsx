'use client';

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import CallBackForm from "../CallBackForm";

export default function MainHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const pathname = usePathname();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (isPopupOpen) {
          setIsPopupOpen(false);
        } else if (isMobileMenuOpen) {
          setIsMobileMenuOpen(false);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileMenuOpen, isPopupOpen]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleCallBackClick = () => {
    // Check if user is on homepage
    if (pathname === '/') {
      // Scroll to hero form section
      const heroSection = document.getElementById('hero-form');
      if (heroSection) {
        heroSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    } else {
      // Open popup with form
      setIsPopupOpen(true);
    }
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  // Sticky positioning is handled by CSS classes - no JavaScript needed

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMobileMenuOpen && !target.closest('nav') && !target.closest('button')) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isMobileMenuOpen]);

  return (
    <>
      {/* Sticky Header */}
      <header className="sticky top-0 w-full bg-[#FDFDFD] shadow-md z-[9999]">

        {/* Navigation Bar */}
        <nav className="w-full text-[#2B2B2B] h-[90px] flex items-center border-b border-gray-200">
          <div className="px-4 sm:px-6 w-full">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <div className="flex items-center">
                <Link href="/">
                  <Image
                    src="/Website Logo - Black Text.svg"
                    alt="Indira Dental Clinic"
                    width={400}
                    height={100}
                    className="h-14 sm:h-16 md:h-14 lg:h-16 xl:h-18 w-auto cursor-pointer"
                  />
                </Link>
              </div>

              {/* Desktop Navigation and Button - Only show on lg and above */}
              <div className="hidden lg:flex items-center gap-4 lg:gap-8">
                <nav className="flex gap-3 lg:gap-6 xl:gap-8 text-sm lg:text-base" style={{ fontWeight: 400 }}>
                  <Link href="/" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Home
                  </Link>
                  <Link href="/services" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Services
                  </Link>
                  <Link href="/offers" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Offers
                  </Link>
                  <Link href="/blog" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Blog
                  </Link>
                  <Link href="/about" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    About
                  </Link>
                  <Link href="/dentists" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Dentists
                  </Link>
                  <Link href="/faq" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    FAQ
                  </Link>
                  <Link href="/contact" className="hover:text-[#E53274] transition-colors" style={{ fontWeight: 400 }}>
                    Contact
                  </Link>
                </nav>

                {/* Desktop Call Back Button */}
                <button
                  onClick={handleCallBackClick}
                  className="bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center gap-2 px-3 lg:px-4 py-2 rounded-md transition text-xs lg:text-sm whitespace-nowrap border-2 border-white"
                >
                  <svg className="w-3.5 h-3.5 lg:w-4 lg:h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  <span className="hidden lg:inline">Get A Call Back</span>
                  <span className="lg:hidden">Call Back</span>
                </button>
              </div>

              {/* Mobile/Tablet Menu Button and Call Back Button - show below lg */}
              <div className="lg:hidden flex items-center gap-3">
                {/* Mobile/Tablet Call Back Button */}
                <button
                  onClick={handleCallBackClick}
                  className="bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center gap-1.5 px-2.5 py-2 rounded-md transition text-xs whitespace-nowrap border-2 border-white"
                >
                  <svg className="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  <span>Get A Call Back</span>
                </button>

                {/* Mobile Menu Button */}
                <button
                  onClick={toggleMobileMenu}
                  className="flex flex-col justify-center items-center w-8 h-8 relative"
                  aria-label="Toggle mobile menu"
                  aria-expanded={isMobileMenuOpen}
                  aria-controls="mobile-navigation"
                >
                  <span
                    className={`block w-6 h-0.5 bg-[#2B2B2B] transition-all duration-300 absolute ${
                      isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'
                    }`}
                  ></span>
                  <span
                    className={`block w-6 h-0.5 bg-[#2B2B2B] transition-all duration-300 ${
                      isMobileMenuOpen ? 'opacity-0' : ''
                    }`}
                  ></span>
                  <span
                    className={`block w-6 h-0.5 bg-[#2B2B2B] transition-all duration-300 absolute ${
                      isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'
                    }`}
                  ></span>
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Mobile Navigation Menu - Part of sticky container */}
        <div
          id="mobile-navigation"
          className={`lg:hidden bg-[#FDFDFD] border-t border-gray-200 w-full transition-all duration-300 ${
            isMobileMenuOpen ? 'block' : 'hidden'
          }`}
          aria-hidden={!isMobileMenuOpen}
        >
        <nav className="flex flex-col py-4 px-4 sm:px-6" role="navigation" aria-label="Mobile navigation">
          <Link
            href="/"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Home
          </Link>
          <Link
            href="/services"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Services
          </Link>
          <Link
            href="/offers"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Offers
          </Link>
          <Link
            href="/blog"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Blog
          </Link>
          <Link
            href="/about"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            About
          </Link>
          <Link
            href="/dentists"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Dentists
          </Link>
          <Link
            href="/faq"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base border-b border-gray-100"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            FAQ
          </Link>
          <Link
            href="/contact"
            className="hover:text-[#E53274] transition-colors py-3 text-[#2B2B2B] text-base"
            style={{ fontWeight: 400 }}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Contact
          </Link>
        </nav>
        </div>
      </header>

      {/* Popup Modal for Call Back Form */}
      {isPopupOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000] p-4"
          onClick={closePopup}
          role="dialog"
          aria-modal="true"
          aria-labelledby="popup-title"
        >
          <div
            className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={closePopup}
              className="absolute top-4 right-4 z-10 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 transition-colors"
              aria-label="Close popup"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Popup Header */}
            <div className="bg-gradient-to-r from-[#E53274] to-[#C7205D] text-white p-6 pb-4">
              <h3 id="popup-title" className="text-xl font-bold mb-2" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                Get A Call Back
              </h3>
              <p className="text-sm opacity-90" style={{ fontFamily: 'Inter, sans-serif' }}>
                Fill out the form below and we'll call you back shortly
              </p>
            </div>

            {/* Form Container */}
            <div className="p-4">
              <CallBackForm variant="contact" className="border-0 shadow-none bg-transparent p-0" />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
