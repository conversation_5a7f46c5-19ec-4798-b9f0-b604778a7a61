'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { SmileGalleryCardData } from '@/types/smile-gallery';

interface SmileGalleryCardProps {
  data: SmileGalleryCardData;
}

export default function SmileGalleryCard({ data }: SmileGalleryCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [sliderPosition, setSliderPosition] = useState(50); // Percentage from left
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    updateSliderPosition(e);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      updateSliderPosition(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    updateSliderPositionTouch(e);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isDragging) {
      e.preventDefault(); // Prevent scrolling
      updateSliderPositionTouch(e);
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  const updateSliderPosition = (e: React.MouseEvent) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      setSliderPosition(percentage);
    }
  };

  const updateSliderPositionTouch = (e: React.TouchEvent) => {
    if (containerRef.current && e.touches.length > 0) {
      const rect = containerRef.current.getBoundingClientRect();
      const x = e.touches[0].clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      setSliderPosition(percentage);
    }
  };

  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-lg transition-all duration-300 mx-auto" style={{ width: '400px', height: '400px' }}>
      <div className="relative flex flex-col h-full">
        {/* Image Container with Slider */}
        <div
          ref={containerRef}
          className="relative w-full bg-gray-200 overflow-hidden cursor-ew-resize touch-none"
          style={{ height: '280px' }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Loading Placeholder */}
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-200 z-10">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E53274]"></div>
            </div>
          )}

          {/* Before Image - Full Width */}
          <div className="absolute inset-0">
            <Image
              src={data.beforeImageUrl}
              alt={`${data.title} - Before`}
              fill
              className="object-cover"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            {/* Before Label */}
            <div className="absolute top-4 left-4 z-20">
              <span className="bg-white text-gray-800 px-3 py-1 rounded-full text-sm font-semibold shadow-md"
                    style={{ fontFamily: 'Inter, sans-serif' }}>
                Before
              </span>
            </div>
          </div>

          {/* After Image - Clipped by slider position */}
          <div
            className="absolute inset-0 overflow-hidden"
            style={{ clipPath: `inset(0 0 0 ${sliderPosition}%)` }}
          >
            <Image
              src={data.afterImageUrl}
              alt={`${data.title} - After`}
              fill
              className="object-cover"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            {/* After Label */}
            <div className="absolute top-4 right-4 z-20">
              <span className="bg-[#93D214] text-white px-3 py-1 rounded-full text-sm font-semibold shadow-md"
                    style={{ fontFamily: 'Inter, sans-serif' }}>
                After
              </span>
            </div>
          </div>

          {/* Slider Line */}
          <div
            className="absolute top-0 bottom-0 w-1 bg-[#93D214] cursor-ew-resize z-30 shadow-lg"
            style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
          >
            {/* Slider Handle */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-[#93D214] rounded-full border-2 border-white shadow-lg flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Treatment Info Bar */}
        <div className="bg-[#E53274] text-white py-3 px-4">
          <div className="flex items-center justify-center">
            {/* Treatment Icon */}
            <div className="w-5 h-5 mr-2 flex-shrink-0">
              <Image
                src={data.treatmentIcon || '/smile.svg'}
                alt={`${data.title} icon`}
                width={20}
                height={20}
                className="w-full h-full object-contain filter brightness-0 invert"
              />
            </div>

            {/* Treatment Title */}
            <span className="font-semibold text-base text-center"
                  style={{ fontFamily: 'DM Sans, sans-serif' }}>
              {data.title}
            </span>
          </div>
        </div>

        {/* Treatment Description */}
        <div className="p-4 bg-white flex-1 flex items-center justify-center">
          <p
            className="text-center text-gray-700 leading-relaxed text-sm"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            {data.description}
          </p>
        </div>
      </div>
    </div>
  );
}
