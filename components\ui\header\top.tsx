'use client';

export default function TopHeader() {
  return (
    <div className="w-full bg-[#FDFDFD] text-[#2B2B2B] text-sm font-light py-2"
         style={{
           minHeight: '25px',
           paddingTop: '8px',
           paddingBottom: '8px',
           fontWeight: 300
         }}>
      <div className="px-4 sm:px-6 w-full">
        {/* Mobile Layout - Phone and Email in one row, Address below */}
        <div className="flex flex-col gap-2 sm:hidden"
             style={{
               minHeight: '20px',
               paddingTop: '4px',
               paddingBottom: '4px'
             }}>
          {/* Phone and Email Row */}
          <div className="flex items-center gap-4"
               style={{
                 minHeight: '15px',
                 paddingTop: '4px',
                 paddingBottom: '4px'
               }}>
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-[#2B2B2B]" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
              </svg>
              <span className="text-xs" style={{ fontWeight: 300 }}>
                7010650063
              </span>
            </div>
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-[#2B2B2B]" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
              <span className="text-xs" style={{ fontWeight: 300 }}>
                <EMAIL>
              </span>
            </div>
          </div>
          {/* Address Row */}
          <div className="flex items-center gap-2"
               style={{
                 minHeight: '15px',
                 paddingTop: '4px',
                 paddingBottom: '4px'
               }}>
            <svg className="w-4 h-4 text-[#2B2B2B] flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
            </svg>
            <span className="text-xs leading-tight" style={{ fontWeight: 300 }}>
              3rd Floor, 54, Katpadi Main Rd, Suthanthira Ponvizha Nagar, Gandhi Nagar, Vellore, Tamil Nadu 632006
            </span>
          </div>
        </div>

        {/* Desktop Layout - All in one row */}
        <div className="hidden sm:flex items-center justify-between"
             style={{
               minHeight: '20px',
               paddingTop: '4px',
               paddingBottom: '4px'
             }}>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-[#2B2B2B]" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
              </svg>
              <span className="text-sm" style={{ fontWeight: 300 }}>
                7010650063
              </span>
            </div>
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-[#2B2B2B]" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
              <span className="text-sm" style={{ fontWeight: 300 }}>
                <EMAIL>
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-[#2B2B2B]" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
            </svg>
            <span className="text-sm" style={{ fontWeight: 300 }}>
              3rd Floor, 54, Katpadi Main Rd, Suthanthira Ponvizha Nagar, Gandhi Nagar, Vellore, Tamil Nadu 632006
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
