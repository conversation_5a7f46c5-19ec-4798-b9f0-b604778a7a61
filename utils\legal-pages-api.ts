import { LegalPagePost, LegalPageData } from '@/types/legal-pages';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Fetch legal pages from WordPress API
export async function fetchLegalPages(): Promise<LegalPagePost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/legal_pages/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error(`Failed to fetch legal pages: ${response.status} ${response.statusText}`);
      return [];
    }

    const legalPages: LegalPagePost[] = await response.json();
    console.log('Legal pages API response:', legalPages);
    return legalPages;
  } catch (error) {
    console.error('Error fetching legal pages:', error);
    console.error('API URL:', `${API_BASE_URL}/wp-json/wp/v2/legal_pages/`);
    return [];
  }
}

// Fetch single legal page by slug
export async function fetchLegalPageBySlug(slug: string): Promise<LegalPagePost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/legal_pages?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error(`Failed to fetch legal page: ${response.status} ${response.statusText}`);
      return null;
    }

    const legalPages: LegalPagePost[] = await response.json();
    console.log('Legal page API response for slug:', slug, legalPages);

    if (legalPages.length > 0) {
      return legalPages[0];
    }

    return null;
  } catch (error) {
    console.error('Error fetching legal page by slug:', error);
    console.error('API URL:', `${API_BASE_URL}/wp-json/wp/v2/legal_pages?slug=${slug}`);
    return null;
  }
}

// Helper function to extract text from HTML
function extractTextFromHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').trim();
}

// Process legal pages data for easier consumption
export async function processLegalPagesData(posts: LegalPagePost[]): Promise<LegalPageData[]> {
  const processedData: LegalPageData[] = [];

  for (const post of posts) {
    try {
      // Safely extract title - handle both object and string formats
      let title = 'Untitled';
      if (typeof post.title === 'object' && post.title?.rendered) {
        title = post.title.rendered;
      } else if (typeof post.title === 'string') {
        title = post.title;
      }

      // Safely extract excerpt - handle both object and string formats
      let excerptText = '';
      if (typeof post.excerpt === 'object' && post.excerpt?.rendered) {
        excerptText = post.excerpt.rendered;
      } else if (typeof post.excerpt === 'string') {
        excerptText = post.excerpt;
      }

      // Safely extract content - handle both object and string formats
      let contentText = '';
      if (typeof post.content === 'object' && post.content?.rendered) {
        contentText = post.content.rendered;
      } else if (typeof post.content === 'string') {
        contentText = post.content;
      }

      const excerpt = extractTextFromHtml(excerptText) ||
                     extractTextFromHtml(contentText).substring(0, 150) + '...';

      processedData.push({
        id: post.id,
        title,
        excerpt,
        slug: post.slug,
        content: contentText,
        acf: post.acf || {}
      });
    } catch (error) {
      console.error(`Error processing legal page post ${post.id}:`, error);
      console.error('Post data:', post);
    }
  }

  return processedData;
}

// Get all legal pages data
export async function getAllLegalPages(): Promise<LegalPageData[]> {
  const posts = await fetchLegalPages();
  return await processLegalPagesData(posts);
}

// Get single legal page data by slug
export async function getLegalPageBySlug(slug: string): Promise<LegalPageData | null> {
  const post = await fetchLegalPageBySlug(slug);
  if (!post) return null;

  const processedData = await processLegalPagesData([post]);
  return processedData.length > 0 ? processedData[0] : null;
}
