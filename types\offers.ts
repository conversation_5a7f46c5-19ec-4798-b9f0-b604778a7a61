// WordPress offer post structure
export interface OfferPost {
  id?: number;
  date?: string;
  date_gmt?: string;
  guid?: {
    rendered?: string;
  };
  modified?: string;
  modified_gmt?: string;
  slug?: string;
  status?: string;
  type?: string;
  link?: string;
  title?: {
    rendered?: string;
  };
  content?: {
    rendered?: string;
    protected?: boolean;
  };
  excerpt?: {
    rendered?: string;
    protected?: boolean;
  };
  author?: number;
  featured_media?: number;
  comment_status?: string;
  ping_status?: string;
  sticky?: boolean;
  template?: string;
  format?: string;
  meta?: any[];
  categories?: number[];
  tags?: number[];
  acf?: OfferACF;
  _links?: any;
}

// ACF fields for offers
export interface OfferACF {
  offer_start_date?: string;
  offer_end_date?: string;
  [key: string]: any;
}

// Simplified offer card data for display
export interface OfferCardData {
  id: number;
  title: string;
  excerpt: string;
  slug: string;
  featured_media: number;
  start_date?: string;
  end_date?: string;
  content?: string;
  acf?: OfferACF;
}

// Featured media response structure
export interface FeaturedMediaResponse {
  id: number;
  source_url: string;
  alt_text: string;
  media_details: {
    width: number;
    height: number;
    sizes: {
      [key: string]: {
        source_url: string;
        width: number;
        height: number;
      };
    };
  };
}
