'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ClinicPost } from '@/types/clinic';
import { fetchClinicBySlug } from '@/utils/clinic-api';
import { ServiceCardData } from '@/types/services';
import { getAllServices } from '@/utils/services-api';
import { DentistCardData } from '@/types/dentist';
import { getAllDentists } from '@/utils/dentist-api';

export default function ClinicPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [clinic, setClinic] = useState<ClinicPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [clinicImages, setClinicImages] = useState<string[]>([]);
  const [services, setServices] = useState<ServiceCardData[]>([]);
  const [dentists, setDentists] = useState<DentistCardData[]>([]);
  const [googleReviews, setGoogleReviews] = useState<any[]>([]);
  const [placeData, setPlaceData] = useState<{
    rating: number;
    user_ratings_total: number;
    reviews: any[];
    name: string;
  } | null>(null);

  // Fetch clinic images from WordPress media API
  const fetchClinicImages = async (imageIds: number[]) => {
    const images: string[] = [];
    for (const imageId of imageIds) {
      if (imageId) {
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_WORDPRESS_API_URL}/wp-json/wp/v2/media/${imageId}`);
          if (response.ok) {
            const mediaData = await response.json();
            images.push(mediaData.source_url);
          }
        } catch (error) {
          console.error('Error fetching image:', error);
        }
      }
    }
    return images;
  };

  // Fetch Google Places data using our API route
  const fetchGooglePlacesData = async (placeId: string) => {
    try {
      const response = await fetch(`/api/google-places/${placeId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.status === 'success') {
          return data;
        }
      }
      console.error('Failed to fetch Google Places data:', response.status);
    } catch (error) {
      console.error('Error fetching Google Places data:', error);
    }
    return null;
  };

  // Fetch clinic data
  useEffect(() => {
    const fetchClinicData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch clinic data
        const clinicData = await fetchClinicBySlug(slug);
        if (clinicData) {
          setClinic(clinicData);

          // Fetch clinic images
          if (clinicData.acf) {
            const imageIds = [
              clinicData.acf.clinic_image_1,
              clinicData.acf.clinic_image_2,
              clinicData.acf.clinic_image_3,
              clinicData.acf.clinic_image_4,
              clinicData.acf.clinic_image_5
            ].filter(id => id && typeof id === 'number') as number[];

            if (imageIds.length > 0) {
              const images = await fetchClinicImages(imageIds);
              setClinicImages(images);
            }

            // Fetch Google Places data if place_id is available
            if (clinicData.acf.clinic_location?.place_id) {
              const placesData = await fetchGooglePlacesData(clinicData.acf.clinic_location.place_id);
              if (placesData) {
                setPlaceData(placesData);
                setGoogleReviews(placesData.reviews || []);
              }
            }
          }
        } else {
          setError('Clinic not found');
        }

        // Fetch services data
        const servicesData = await getAllServices();
        setServices(servicesData);

        // Fetch dentists data
        const dentistsData = await getAllDentists();
        setDentists(dentistsData);

      } catch (error) {
        console.error('Error fetching clinic data:', error);
        setError('Failed to load clinic');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchClinicData();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#E53274' }}></div>
          <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>Loading clinic information...</p>
        </div>
      </div>
    );
  }

  if (error || !clinic) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            {error || 'Clinic Not Found'}
          </h1>
          <Link href="/" className="text-blue-600 hover:underline" style={{ fontFamily: 'Inter, sans-serif' }}>
            Return to Homepage
          </Link>
        </div>
      </div>
    );
  }

  // Helper function to render star rating
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <svg
          key={i}
          className={`w-4 h-4 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }
    return stars;
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#FDFDFD' }}>
      {/* Header Section */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2">
            {/* Clinic Header */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold mb-2" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                {clinic.title.rendered}
              </h1>

              <div className="flex items-center gap-2 mb-2">
                <div className="flex">
                  {renderStars(placeData?.rating || 5)}
                </div>
                <span className="text-sm text-gray-600">
                  {placeData ?
                    `${placeData.rating.toFixed(1)} (${placeData.user_ratings_total} reviews)` :
                    '4.8 (127 reviews)'
                  }
                </span>
              </div>

              <p className="text-gray-600 mb-4" style={{ fontFamily: 'Inter, sans-serif' }}>
                Dental Clinic in Vellore
              </p>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 mb-6">
                <Link href="/book-appointment"
                      className="px-6 py-2 text-white rounded-lg font-medium transition-all duration-300 hover:scale-105"
                      style={{ backgroundColor: '#E53274', fontFamily: 'DM Sans, sans-serif' }}>
                  Book Appointment
                </Link>
                <a href="tel:+917010650063"
                   className="px-6 py-2 border-2 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                   style={{
                     borderColor: '#E53274',
                     color: '#E53274',
                     fontFamily: 'DM Sans, sans-serif'
                   }}>
                  Call
                </a>
                {clinic.acf?.clinic_location?.place_id && (
                  <a href={`https://www.google.com/maps/place/?q=place_id:${clinic.acf.clinic_location.place_id}`}
                     target="_blank"
                     rel="noopener noreferrer"
                     className="px-6 py-2 border-2 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                     style={{
                       borderColor: '#575C8D',
                       color: '#575C8D',
                       fontFamily: 'DM Sans, sans-serif'
                     }}>
                    Give Review
                  </a>
                )}

              </div>
            </div>

            {/* Photos & Videos Section */}
            <div className="mb-8">
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                Photos & Videos
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {clinicImages.length > 0 ? (
                  clinicImages.slice(0, 6).map((imageUrl, index) => (
                    <div key={index} className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                      <Image
                        src={imageUrl}
                        alt={`Clinic photo ${index + 1}`}
                        width={200}
                        height={200}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  ))
                ) : (
                  // Placeholder images
                  [1, 2, 3, 4, 5, 6].map((index) => (
                    <div key={index} className="aspect-square bg-gray-200 rounded-lg overflow-hidden flex items-center justify-center">
                      <div className="text-center">
                        <svg className="w-8 h-8 mx-auto mb-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-xs text-gray-500">Photo {index}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Dentist Details Section */}
            {dentists.length > 0 && (
              <div className="mb-8">
                <h3 className="text-xl font-bold mb-6" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Meet Our Expert Dentists
                </h3>
                <div className="space-y-8">
                  {dentists.map((dentist) => (
                    <div key={dentist.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
                      <div className="grid lg:grid-cols-3 gap-6">
                        {/* Left Column - Dentist Image */}
                        <div className="flex-shrink-0">
                          {dentist.image_url ? (
                            <div className="w-full max-w-xs mx-auto lg:max-w-none rounded-lg overflow-hidden">
                              <Image
                                src={dentist.image_url}
                                alt={dentist.title}
                                width={300}
                                height={300}
                                className="w-full h-auto object-cover"
                              />
                            </div>
                          ) : (
                            <div className="w-full max-w-xs mx-auto lg:max-w-none aspect-square rounded-lg bg-gray-200 flex items-center justify-center">
                              <svg className="w-24 h-24 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                              </svg>
                            </div>
                          )}
                        </div>

                        {/* Middle Column - Basic Info */}
                        <div className="lg:col-span-1">
                          <h4 className="text-2xl font-bold mb-2" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                            {dentist.title}
                          </h4>

                          {/* Designation */}
                          {dentist.designation && (
                            <p className="text-lg font-semibold mb-3" style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                              {dentist.designation}
                            </p>
                          )}

                          {/* Qualification */}
                          {dentist.qualification && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Qualification:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.qualification}
                              </p>
                            </div>
                          )}

                          {/* Experience */}
                          {dentist.experience_years && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Experience:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.experience_years} years
                              </p>
                            </div>
                          )}

                          {/* Specialities */}
                          {dentist.speciality_names && dentist.speciality_names.length > 0 && (
                            <div className="mb-4">
                              <h5 className="font-semibold mb-2" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Specializations:
                              </h5>
                              <div className="flex flex-wrap gap-2">
                                {dentist.speciality_names.map((speciality, index) => (
                                  <span
                                    key={index}
                                    className="px-3 py-1 text-xs rounded-full border"
                                    style={{
                                      borderColor: '#E53274',
                                      color: '#E53274',
                                      backgroundColor: '#FFF5F8',
                                      fontFamily: 'Inter, sans-serif'
                                    }}
                                  >
                                    {speciality}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Location */}
                          {dentist.clinic_location && (
                            <div className="flex items-center gap-2 mb-4">
                              <svg className="w-4 h-4" style={{ color: '#575C8D' }} fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                              </svg>
                              <span className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.clinic_location.city}, {dentist.clinic_location.state}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Right Column - Additional Details */}
                        <div className="lg:col-span-1">
                          {/* Bio/Description */}
                          {(dentist.bio || dentist.excerpt) && (
                            <div className="mb-4">
                              <h5 className="font-semibold mb-2" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                About:
                              </h5>
                              <div
                                className="text-sm leading-relaxed"
                                style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', lineHeight: '1.6' }}
                                dangerouslySetInnerHTML={{ __html: dentist.bio || dentist.excerpt }}
                              />
                            </div>
                          )}

                          {/* Education */}
                          {dentist.education && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Education:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.education}
                              </p>
                            </div>
                          )}

                          {/* Languages Spoken */}
                          {dentist.languages_spoken && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Languages:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.languages_spoken}
                              </p>
                            </div>
                          )}

                          {/* Consultation Hours */}
                          {dentist.consultation_hours && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Consultation Hours:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.consultation_hours}
                              </p>
                            </div>
                          )}

                          {/* Years of Practice */}
                          {dentist.years_of_practice && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Years of Practice:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.years_of_practice} years
                              </p>
                            </div>
                          )}

                          {/* Treatments Offered */}
                          {dentist.treatments_offered && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Treatments Offered:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.treatments_offered}
                              </p>
                            </div>
                          )}

                          {/* Professional Memberships */}
                          {dentist.professional_memberships && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Professional Memberships:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.professional_memberships}
                              </p>
                            </div>
                          )}

                          {/* Awards & Achievements */}
                          {dentist.awards_achievements && (
                            <div className="mb-3">
                              <h5 className="font-semibold mb-1" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Awards & Achievements:
                              </h5>
                              <p className="text-sm" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                {dentist.awards_achievements}
                              </p>
                            </div>
                          )}

                          {/* Contact Information */}
                          <div className="space-y-2 mb-4">
                            {dentist.phone_number && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" style={{ color: '#575C8D' }} fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                </svg>
                                <a href={`tel:${dentist.phone_number}`}
                                   className="text-sm hover:text-[#E53274] transition-colors"
                                   style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                  {dentist.phone_number}
                                </a>
                              </div>
                            )}
                            {dentist.email && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" style={{ color: '#575C8D' }} fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                </svg>
                                <a href={`mailto:${dentist.email}`}
                                   className="text-sm hover:text-[#E53274] transition-colors"
                                   style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                  {dentist.email}
                                </a>
                              </div>
                            )}
                            {dentist.website && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" style={{ color: '#575C8D' }} fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd"/>
                                </svg>
                                <a href={dentist.website}
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   className="text-sm hover:text-[#E53274] transition-colors"
                                   style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                                  Visit Website
                                </a>
                              </div>
                            )}
                          </div>

                          {/* Social Media Links */}
                          {(dentist.linkedin || dentist.facebook || dentist.instagram) && (
                            <div className="mb-4">
                              <h5 className="font-semibold mb-2" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                                Connect:
                              </h5>
                              <div className="flex gap-3">
                                {dentist.linkedin && (
                                  <a href={dentist.linkedin}
                                     target="_blank"
                                     rel="noopener noreferrer"
                                     className="text-[#575C8D] hover:text-[#E53274] transition-colors">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd"/>
                                    </svg>
                                  </a>
                                )}
                                {dentist.facebook && (
                                  <a href={dentist.facebook}
                                     target="_blank"
                                     rel="noopener noreferrer"
                                     className="text-[#575C8D] hover:text-[#E53274] transition-colors">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd"/>
                                    </svg>
                                  </a>
                                )}
                                {dentist.instagram && (
                                  <a href={dentist.instagram}
                                     target="_blank"
                                     rel="noopener noreferrer"
                                     className="text-[#575C8D] hover:text-[#E53274] transition-colors">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd"/>
                                    </svg>
                                  </a>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex flex-wrap gap-3">
                            <Link href="/book-appointment"
                                  className="px-4 py-2 text-white rounded-lg font-medium transition-all duration-300 hover:scale-105 text-sm"
                                  style={{ backgroundColor: '#E53274', fontFamily: 'DM Sans, sans-serif' }}>
                              Book Appointment
                            </Link>
                            <a href={`tel:${dentist.phone_number || '+917010650063'}`}
                               className="px-4 py-2 border-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 text-sm"
                               style={{
                                 borderColor: '#575C8D',
                                 color: '#575C8D',
                                 fontFamily: 'DM Sans, sans-serif'
                               }}>
                              Call Now
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Business Info */}
          <div className="lg:col-span-1">
            {/* Business Info Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6 shadow-sm">
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                Business Info
              </h3>

              {/* Address */}
              {clinic.acf?.clinic_location && (
                <div className="mb-4">
                  <div className="flex items-start gap-3">
                    <svg className="w-5 h-5 mt-0.5" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                    </svg>
                    <div>
                      <p className="font-medium" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                        {clinic.acf.clinic_location.address}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Phone */}
              <div className="mb-4">
                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  <a href="tel:+917010650063"
                     className="font-medium hover:text-[#E53274] transition-colors"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                    +91-7010650063
                  </a>
                </div>
              </div>

              {/* Website */}
              <div className="mb-4">
                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd"/>
                  </svg>
                  <a href="https://dentalclinicinvellore.in"
                     target="_blank"
                     rel="noopener noreferrer"
                     className="font-medium hover:text-[#E53274] transition-colors"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                    dentalclinicinvellore.in
                  </a>
                </div>
              </div>
            </div>

            {/* Location & Hours */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6 shadow-sm">
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                Location & Hours
              </h3>

              {/* Map */}
              <div className="bg-gray-200 rounded-lg h-48 mb-4 overflow-hidden">
                {clinic.acf?.clinic_location && (
                  <iframe
                    src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${clinic.acf.clinic_location.lat},${clinic.acf.clinic_location.lng}&zoom=${clinic.acf.clinic_location.zoom || 15}`}
                    width="100%"
                    height="100%"
                    style={{ border: 0, borderRadius: '8px' }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Clinic Location"
                  ></iframe>
                )}
              </div>

              {/* Hours */}
              <div className="space-y-3">
                {[
                  { day: 'Monday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Tuesday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Wednesday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Thursday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Friday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Saturday', hours: '10:00 AM - 8:00 PM' },
                  { day: 'Sunday', hours: '10:00 AM - 1:30 PM' }
                ].map((item, index) => (
                  <div key={index} className="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <span className="font-medium" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>{item.day}</span>
                    <span style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>{item.hours}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Services Offered Section */}
        <div className="mt-8">
          <h3 className="text-2xl md:text-3xl font-bold mb-8" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Services Offered
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {services.map((service, index) => (
              <Link key={index} href={`/services/${service.slug}`}
                    className="flex items-center gap-3 p-4 bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-[#E53274]">
                <div className="w-8 h-8 flex-shrink-0">
                  <Image
                    src={service.icon}
                    alt={service.title}
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                    style={{ filter: 'hue-rotate(320deg) saturate(1.5)' }}
                  />
                </div>
                <span className="font-medium" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  {service.title}
                </span>
              </Link>
            ))}
          </div>
        </div>

        {/* About Section */}
        <div className="mt-12">
          <h3 className="text-2xl md:text-3xl font-bold mb-8" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            About {clinic.title.rendered}
          </h3>

          <div className="prose max-w-none">
            <div
              className="text-base leading-relaxed"
              style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', lineHeight: '1.7' }}
              dangerouslySetInnerHTML={{ __html: clinic.content.rendered }}
            />
          </div>

          {/* Facilities */}
          <div className="mt-12">
            <h4 className="text-xl font-bold mb-6" style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
              Facilities & Technologies
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                'Digital X-Ray',
                'Intraoral Camera',
                'Laser Dentistry',
                'Sterilization Equipment',
                'Air Conditioning',
                'Wheelchair Accessible',
                'Free Wi-Fi',
                'Parking Available',
                'Emergency Services',
                'Insurance Accepted'
              ].map((facility, index) => (
                <div key={index} className="flex items-center gap-3 p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-300"
                     style={{ backgroundColor: '#FDFDFD' }}>
                  <svg className="w-5 h-5 flex-shrink-0" style={{ color: '#575C8D' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                    {facility}
                  </span>
                </div>
              ))}
            </div>
          </div>

        </div>

        {/* Google Reviews Section */}
        <div className="mt-12">
          <h3 className="text-2xl md:text-3xl font-bold mb-8" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Google Reviews {placeData && `(${placeData.user_ratings_total})`}
          </h3>

          <div className="space-y-6">
            {googleReviews.length > 0 ? (
              googleReviews.slice(0, 6).map((review, index) => (
                <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start gap-4">
                    {/* Avatar */}
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                         style={{ backgroundColor: '#E53274' }}>
                      {review.author_name?.charAt(0).toUpperCase() || 'U'}
                    </div>

                    {/* Review Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                          {review.author_name || 'Anonymous'}
                        </h4>
                        <div className="flex">
                          {renderStars(review.rating || 5)}
                        </div>
                        <span className="text-sm text-gray-500">
                          {review.relative_time_description || 'Recently'}
                        </span>
                      </div>

                      <p style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', lineHeight: '1.6' }}>
                        {review.text || 'Great experience at this clinic!'}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              // Fallback reviews if Google reviews are not available
              [
                {
                  name: 'Priya S.',
                  rating: 5,
                  date: 'Dec 1, 2024',
                  text: 'Excellent service and very professional staff. Dr. Rockson is very skilled and gentle. The clinic is clean and well-maintained. Highly recommended!'
                },
                {
                  name: 'Rajesh K.',
                  rating: 5,
                  date: 'Nov 28, 2024',
                  text: 'Great experience with root canal treatment. The doctor explained everything clearly and the procedure was painless. Very satisfied with the results.'
                },
                {
                  name: 'Anitha M.',
                  rating: 5,
                  date: 'Nov 25, 2024',
                  text: 'Best dental clinic in Vellore. Modern equipment and excellent treatment. The staff is very friendly and accommodating.'
                }
              ].map((review, index) => (
                <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start gap-4">
                    {/* Avatar */}
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                         style={{ backgroundColor: '#E53274' }}>
                      {review.name.charAt(0).toUpperCase()}
                    </div>

                    {/* Review Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                          {review.name}
                        </h4>
                        <div className="flex">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-sm text-gray-500">
                          {review.date}
                        </span>
                      </div>

                      <p style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', lineHeight: '1.6' }}>
                        {review.text}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* View More Reviews Button */}
            {clinic.acf?.clinic_location?.place_id && (
              <div className="text-center mt-8">
                <a href={`https://www.google.com/maps/place/?q=place_id:${clinic.acf.clinic_location.place_id}`}
                   target="_blank"
                   rel="noopener noreferrer"
                   className="inline-block px-8 py-3 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105 hover:shadow-lg"
                   style={{
                     backgroundColor: '#575C8D',
                     fontFamily: 'DM Sans, sans-serif'
                   }}>
                  View All Reviews on Google
                </a>
              </div>
            )}
          </div>
        </div>
      </div>

    </div>
  );
}
