'use client';

import Image from 'next/image';
import Link from 'next/link';
import { BlogCardData } from '@/types/blog';

interface BlogCardProps {
  post: BlogCardData;
  className?: string;
}

export default function BlogCard({ post, className = '' }: BlogCardProps) {
  return (
    <article className={`bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300 ${className}`}>
      {/* Featured Image */}
      <div className="relative h-48 bg-gray-200">
        {post.featuredImage ? (
          <Image
            src={post.featuredImage}
            alt={post.title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-[#E53274] to-[#575C8D] flex items-center justify-center">
            <i className="fas fa-image text-white text-3xl opacity-50"></i>
          </div>
        )}
        
        {/* Overlay with post meta */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <div className="flex items-center text-white text-xs space-x-4">
            <span style={{ fontFamily: 'Inter, sans-serif' }}>
              {post.date}
            </span>
            <span style={{ fontFamily: 'Inter, sans-serif' }}>
              BY {post.author.toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <h2 className="text-lg font-bold mb-3 line-clamp-2 hover:text-[#E53274] transition-colors">
          <Link 
            href={`/blog/${post.slug}`}
            style={{ 
              fontFamily: 'DM Sans, sans-serif',
              color: '#2b2b2b'
            }}
          >
            {post.title}
          </Link>
        </h2>

        {/* Excerpt */}
        <p className="text-sm leading-relaxed mb-4 line-clamp-3"
           style={{ 
             fontFamily: 'Inter, sans-serif',
             color: '#666666'
           }}>
          {post.excerpt}
        </p>

        {/* Categories */}
        {post.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {post.categories.slice(0, 2).map((category, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs rounded-full"
                style={{ 
                  backgroundColor: '#F3F4FA',
                  color: '#575C8D',
                  fontFamily: 'Inter, sans-serif'
                }}
              >
                {category}
              </span>
            ))}
          </div>
        )}

        {/* Read More Link */}
        <Link 
          href={`/blog/${post.slug}`}
          className="inline-flex items-center text-sm font-medium hover:underline transition-colors"
          style={{ 
            color: '#E53274',
            fontFamily: 'DM Sans, sans-serif'
          }}
        >
          Read More
          <i className="fas fa-arrow-right ml-2 text-xs"></i>
        </Link>
      </div>
    </article>
  );
}
