export interface LegalPagePost {
  id: number;
  date?: string;
  date_gmt?: string;
  guid?: {
    rendered: string;
  };
  modified?: string;
  modified_gmt?: string;
  slug: string;
  status?: string;
  type?: string;
  link?: string;
  title: {
    rendered: string;
  } | string;
  content?: {
    rendered: string;
    protected?: boolean;
  } | string;
  excerpt?: {
    rendered: string;
    protected?: boolean;
  } | string;
  featured_media?: number;
  template?: string;
  meta?: {
    _acf_changed?: boolean;
    [key: string]: any;
  };
  class_list?: string[];
  acf?: {
    [key: string]: any;
  };
  _links?: any;
}

export interface LegalPageData {
  id: number;
  title: string;
  excerpt: string;
  slug: string;
  content?: string;
  acf?: {
    [key: string]: any;
  };
}
