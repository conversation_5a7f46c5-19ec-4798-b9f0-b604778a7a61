'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { fetchCategories } from '@/utils/blog-api';
import { BlogCategory } from '@/types/blog';

interface CategoriesProps {
  className?: string;
}

export default function Categories({ className = '' }: CategoriesProps) {
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        // Filter out uncategorized and limit to main categories
        const filteredCategories = categoriesData
          .filter(cat => cat.name.toLowerCase() !== 'uncategorized' && cat.count > 0)
          .slice(0, 10); // Show up to 10 categories
        setCategories(filteredCategories);
      } catch (error) {
        console.error('Error loading categories:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  return (
    <div className={`bg-white rounded-lg p-6 shadow-sm border border-gray-100 ${className}`}>
      {/* Categories Header */}
      <h3 className="text-lg font-bold mb-4" 
          style={{ 
            fontFamily: 'DM Sans, sans-serif',
            color: '#2b2b2b'
          }}>
        Categories
      </h3>

      {/* Categories Description */}
      <p className="text-sm mb-4 leading-relaxed"
         style={{
           fontFamily: 'Inter, sans-serif',
           color: '#666666'
         }}>
        Explore dental health topics organized by category.
      </p>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-[#E53274]"></div>
          <p className="text-sm mt-2" style={{ fontFamily: 'Inter, sans-serif', color: '#666666' }}>
            Loading categories...
          </p>
        </div>
      )}

      {/* No Categories State */}
      {!loading && categories.length === 0 && (
        <p className="text-sm text-center py-4"
           style={{
             fontFamily: 'Inter, sans-serif',
             color: '#666666'
           }}>
          No categories available.
        </p>
      )}

      {/* Categories List */}
      {!loading && categories.length > 0 && (
        <ul className="space-y-3">
          {categories.map((category) => (
            <li key={category.id} className="flex items-center">
              {/* Category Icon */}
              <div className="w-2 h-2 rounded-full mr-3 flex-shrink-0"
                   style={{ backgroundColor: '#4A90E2' }}>
              </div>

              {/* Category Link */}
              <Link
                href={`/blog?category=${category.slug}`}
                className="text-sm hover:underline transition-colors flex-1"
                style={{
                  fontFamily: 'Inter, sans-serif',
                  color: '#2b2b2b'
                }}
              >
                {category.name} ({category.count})
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
