'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { OfferPost } from '@/types/offers';
import { fetchOfferBySlug, fetchFeaturedMedia, formatOfferDate, isOfferActive } from '@/utils/offers-api';
import ContactInfoSection from '@/components/ui/ContactInfoSection';

export default function OfferDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [offer, setOffer] = useState<OfferPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [featuredImage, setFeaturedImage] = useState<string>('');

  useEffect(() => {
    const fetchOffer = async () => {
      try {
        setLoading(true);
        const offerData = await fetchOfferBySlug(slug);
        
        if (!offerData) {
          setError('Offer not found');
          return;
        }

        setOffer(offerData);

        // Fetch featured image
        if (offerData.featured_media) {
          const imageUrl = await fetchFeaturedMedia(offerData.featured_media);
          setFeaturedImage(imageUrl);
        }
      } catch (err) {
        console.error('Error fetching offer:', err);
        setError('Failed to load offer. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchOffer();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274] mb-4"></div>
          <p className="text-lg font-semibold text-[#E53274]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Loading Offer...
          </p>
        </div>
      </div>
    );
  }

  if (error || !offer) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <p className="text-lg font-semibold text-red-600 mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {error || 'Offer not found'}
          </p>
          <Link
            href="/offers"
            className="bg-[#E53274] hover:bg-[#C7205D] text-white px-6 py-2 rounded-md transition-colors"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            Back to Offers
          </Link>
        </div>
      </div>
    );
  }

  const isActive = offer?.acf?.offer_start_date && offer?.acf?.offer_end_date
    ? isOfferActive(offer.acf.offer_start_date, offer.acf.offer_end_date)
    : false;
  const startDate = offer?.acf?.offer_start_date ? formatOfferDate(offer.acf.offer_start_date) : '';
  const endDate = offer?.acf?.offer_end_date ? formatOfferDate(offer.acf.offer_end_date) : '';

  // Safe access to offer properties
  const offerTitle = typeof offer?.title === 'string'
    ? offer.title
    : offer?.title?.rendered || 'Special Offer';
  const offerExcerpt = typeof offer?.excerpt === 'string'
    ? offer.excerpt
    : offer?.excerpt?.rendered || '';
  const offerContent = typeof offer?.content === 'string'
    ? offer.content
    : offer?.content?.rendered || '';

  return (
    <div className="w-full">
      {/* Hero Section with Featured Image */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-[#FFF5F8] to-[#F3F4FA]">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              {isActive && (
                <div className="inline-flex items-center gap-2 bg-[#93D214] text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Limited Time Offer
                </div>
              )}
              
              <h1 className="text-4xl md:text-5xl font-bold text-[#2b2b2b] mb-6" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                {offerTitle}
              </h1>

              {/* Offer Dates */}
              {(startDate || endDate) && (
                <div className="mb-6 space-y-3">
                  {endDate && (
                    <div className="flex items-center gap-3 text-[#E53274]">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      <span className="font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>Valid until: {endDate}</span>
                    </div>
                  )}
                </div>
              )}

              <div className="text-lg text-[#2b2b2b] mb-8 leading-relaxed" style={{ fontFamily: 'Inter, sans-serif' }}>
                <div dangerouslySetInnerHTML={{ __html: offerExcerpt }} />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/book-appointment"
                  className="bg-[#E53274] hover:bg-[#C7205D] text-white text-center py-3 px-8 rounded-md transition-colors font-medium"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Book Your Appointment
                </Link>
                <Link
                  href="/contact"
                  className="border-2 border-[#E53274] text-[#E53274] hover:bg-[#E53274] hover:text-white text-center py-3 px-8 rounded-md transition-colors font-medium"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Contact Us
                </Link>
              </div>
            </div>

            {/* Right Image */}
            {featuredImage && (
              <div className="relative">
                <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                  <Image
                    src={featuredImage}
                    alt={offerTitle}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </section>



      {/* Benefits Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FFF5F8' }}>
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#E53274] text-center mb-12" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Benefits
          </h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-[#93D214] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#2b2b2b]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  Professional dental care
                </h3>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-[#93D214] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#2b2b2b]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  Experienced specialists
                </h3>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-[#93D214] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#2b2b2b]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  State-of-the-art equipment
                </h3>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-[#93D214] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#2b2b2b]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  Comfortable environment
                </h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-[#E53274] to-[#575C8D]">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Ready to Transform Your Smile?
          </h2>
          <p className="text-lg text-white mb-8 leading-relaxed" style={{ fontFamily: 'Inter, sans-serif' }}>
            Take advantage of this special offer today and experience the exceptional dental care at Indira Dental Clinic. Our team of experts is ready to help you achieve the smile you've always wanted.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/book-appointment"
              className="bg-white text-[#E53274] hover:bg-gray-100 py-3 px-8 rounded-md transition-colors font-medium"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Book Your Appointment
            </Link>
            <Link
              href="/contact"
              className="border-2 border-white text-white hover:bg-white hover:text-[#E53274] py-3 px-8 rounded-md transition-colors font-medium"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
