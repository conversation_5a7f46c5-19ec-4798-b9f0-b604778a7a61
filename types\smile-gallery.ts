export interface SmileGalleryPost {
  id: number;
  date: string;
  date_gmt: string;
  guid: {
    rendered: string;
  };
  modified: string;
  modified_gmt: string;
  slug: string;
  status: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    protected: boolean;
  };
  excerpt: {
    rendered: string;
    protected: boolean;
  };
  featured_media: number;
  template: string;
  meta: {
    _acf_changed: boolean;
  };
  class_list: string[];
  acf: {
    before_image: number;
    after_image: number;
  };
  _links: any;
}

export interface MediaResponse {
  guid: {
    rendered: string;
  };
}

export interface SmileGalleryCardData {
  id: number;
  title: string;
  beforeImageUrl: string;
  afterImageUrl: string;
  description: string;
  treatmentIcon?: string;
}
