'use client';

import Image from 'next/image';
import { BlogAuthor } from '@/types/blog';

interface AuthorProfileProps {
  className?: string;
  authorData?: BlogAuthor;
}

export default function AuthorProfile({ className = '', authorData }: AuthorProfileProps) {
  // Default author data if no author data provided
  const defaultAuthor = {
    id: 1,
    name: 'Blog Author',
    description: 'Expert dental professional providing quality care and sharing valuable insights through our blog.',
    avatar_urls: {
      '96': '' // No specific image - will fall back to initials
    },
    url: '',
    link: '',
    slug: 'blog-author',
    meta: []
  };

  // Use provided author data or fall back to default
  const author = authorData || defaultAuthor;

  // Get author avatar URL with proper type handling
  const avatarUrl = author.avatar_urls && typeof author.avatar_urls === 'object'
    ? (author.avatar_urls['96'] || (author.avatar_urls as any)['48'] || Object.values(author.avatar_urls)[0] || '')
    : '';

  // Get author initials for fallback
  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  // Remove title logic - we'll only show the author name
  return (
    <div className={`bg-white rounded-lg p-6 shadow-sm border border-gray-100 ${className}`}>
      {/* Author Profile Header */}
      <h3 className="text-lg font-bold mb-4" 
          style={{ 
            fontFamily: 'DM Sans, sans-serif',
            color: '#2b2b2b'
          }}>
        Author Profile
      </h3>

      {/* Author Info */}
      <div className="flex items-start space-x-4">
        {/* Author Avatar */}
        <div className="flex-shrink-0">
          <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
            {avatarUrl ? (
              <Image
                src={avatarUrl}
                alt={author.name}
                width={64}
                height={64}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to initials if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const initials = getInitials(author.name);
                  target.parentElement!.innerHTML = `<div class="w-full h-full bg-gradient-to-br from-[#E53274] to-[#575C8D] flex items-center justify-center text-white font-bold text-lg">${initials}</div>`;
                }}
              />
            ) : (
              // Show initials directly when no avatar URL
              <div className="w-full h-full bg-gradient-to-br from-[#E53274] to-[#575C8D] flex items-center justify-center text-white font-bold text-lg">
                {getInitials(author.name)}
              </div>
            )}
          </div>
        </div>

        {/* Author Details */}
        <div className="flex-1">
          <h4 className="font-semibold text-lg mb-3"
              style={{
                fontFamily: 'DM Sans, sans-serif',
                color: '#E53274'
              }}>
            {author.name}
          </h4>
          <p className="text-sm leading-relaxed"
             style={{
               fontFamily: 'Inter, sans-serif',
               color: '#666666'
             }}>
            {author.description || 'Expert dental professional providing quality care and sharing valuable insights through our blog.'}
          </p>
        </div>
      </div>
    </div>
  );
}
