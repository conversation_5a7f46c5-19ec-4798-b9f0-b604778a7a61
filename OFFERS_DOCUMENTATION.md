# Offers Functionality Documentation

## Overview
This document describes the offers functionality implemented for Indira Dental Clinic website. The system provides a complete offers management solution with listing and detail pages, SEO optimization, and responsive design.

## Features Implemented

### 1. Offers Listing Page (`/offers`)
- **URL**: `/offers`
- **File**: `app/offers/page.tsx`
- **Features**:
  - Grid layout displaying all available offers
  - Featured images for each offer
  - Offer validity dates (start/end dates)
  - Active offer indicators ("Limited Time Offer" badges)
  - Responsive design (mobile, tablet, desktop)
  - Loading states and error handling
  - SEO optimized metadata

### 2. Dynamic Offer Detail Page (`/offers/[slug]`)
- **URL**: `/offers/{slug}`
- **File**: `app/offers/[slug]/page.tsx`
- **Features**:
  - Dynamic routing based on offer slug
  - Hero section with featured image
  - Offer details and content
  - Benefits section with checkmarks
  - Call-to-action sections
  - SEO metadata generation
  - Responsive design

### 3. Navigation Integration
- Added "Offers" link to main navigation (desktop and mobile)
- **File**: `components/ui/header/main.tsx`
- Positioned between "Services" and "About" in navigation

### 4. API Integration
- **File**: `utils/offers-api.ts`
- **Endpoint**: `{API_URL}/wp-json/wp/v2/offer`
- **Functions**:
  - `fetchOfferPosts()` - Get all offers
  - `fetchOfferBySlug(slug)` - Get single offer by slug
  - `fetchFeaturedMedia(mediaId)` - Get featured image
  - `getAllOffers()` - Get formatted offer data
  - `formatOfferDate()` - Format YYYYMMDD to readable date
  - `isOfferActive()` - Check if offer is currently active

### 5. Type Definitions
- **File**: `types/offers.ts`
- **Types**:
  - `OfferPost` - WordPress API response structure
  - `OfferACF` - ACF fields (start_date, end_date)
  - `OfferCardData` - Simplified data for display
  - `FeaturedMediaResponse` - Media API response

### 6. Reusable Components
- **File**: `components/ui/OfferCard.tsx`
- Modular offer card component
- Handles image loading and error states
- Displays offer dates and active status
- Action buttons for "View Details" and "Book Now"

## WordPress REST API Structure

### Offer Endpoint
```
GET {API_URL}/wp-json/wp/v2/offer
```

### Expected Response Format
```json
[
  {
    "slug": "107",
    "title": {
      "rendered": "Offer Title"
    },
    "excerpt": {
      "rendered": "<p>This is new offer!!!!!!!!</p>\n",
      "protected": false
    },
    "content": {
      "rendered": "<p>Full offer content...</p>",
      "protected": false
    },
    "featured_media": 108,
    "acf": {
      "offer_start_date": "20250623",
      "offer_end_date": "20250630"
    }
  }
]
```

### ACF Fields Required
- `offer_start_date` (YYYYMMDD format)
- `offer_end_date` (YYYYMMDD format)

## SEO Optimization

### Offers Listing Page
- **Title**: "Special Dental Offers - Indira Dental Clinic Vellore | Affordable Dental Care"
- **Description**: Comprehensive description with keywords
- **Keywords**: dental offers, promotions, affordable care, etc.
- **Open Graph**: Social media sharing optimization
- **Canonical URL**: https://dentalclinicinvellore.in/offers

### Dynamic Offer Pages
- **Title**: "{Offer Title} - Special Dental Offer | Indira Dental Clinic Vellore"
- **Description**: Auto-generated from offer excerpt
- **Keywords**: Dynamic based on offer title
- **Open Graph**: Includes featured image
- **Canonical URL**: https://dentalclinicinvellore.in/offers/{slug}

## Design System Compliance

### Colors Used
- **Primary**: #E53274 (brand pink)
- **Secondary**: #575C8D (brand purple)
- **Accent**: #93D214 (brand green)
- **Background**: #FDFDFD (neutral white)
- **Text**: #2b2b2b (dark gray)

### Typography
- **Headings**: DM Sans (weight 700)
- **Body Text**: Inter
- **Buttons**: Inter

### Layout
- **Background Gradients**: From #E53274 to #575C8D
- **Card Design**: White background with shadow
- **Responsive Grid**: 1 column mobile, 2 tablet, 3 desktop

## File Structure
```
app/
├── offers/
│   ├── page.tsx (listing page)
│   └── [slug]/
│       └── page.tsx (detail page)
components/
└── ui/
    └── OfferCard.tsx (reusable card component)
types/
└── offers.ts (TypeScript definitions)
utils/
└── offers-api.ts (API functions)
```

## Usage Examples

### Fetching All Offers
```typescript
import { getAllOffers } from '@/utils/offers-api';

const offers = await getAllOffers();
```

### Fetching Single Offer
```typescript
import { fetchOfferBySlug } from '@/utils/offers-api';

const offer = await fetchOfferBySlug('special-cleaning-offer');
```

### Using Offer Card Component
```tsx
import OfferCard from '@/components/ui/OfferCard';

<OfferCard offer={offerData} />
```

## Testing Recommendations

1. **API Testing**: Verify WordPress REST API endpoints return expected data
2. **Responsive Testing**: Test on mobile, tablet, and desktop devices
3. **SEO Testing**: Verify metadata generation and canonical URLs
4. **Image Loading**: Test with and without featured images
5. **Date Validation**: Test with various date formats and edge cases
6. **Error Handling**: Test with network failures and invalid data

## Future Enhancements

1. **Filtering**: Add filters by offer type, date range, etc.
2. **Search**: Implement offer search functionality
3. **Pagination**: Add pagination for large numbers of offers
4. **Analytics**: Track offer views and conversions
5. **Email Signup**: Newsletter signup for offer notifications
6. **Social Sharing**: Add social media sharing buttons
