'use client';

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import CallBackForm from "@/components/ui/CallBackForm";
import SmileGalleryCard from "@/components/ui/SmileGalleryCard";
import { SmileGalleryCardData } from "@/types/smile-gallery";
import { ServiceCardData } from "@/types/services";
import { getHomepageSmileGallery } from "@/utils/smile-gallery-api";
import { getHomepageServices } from "@/utils/services-api";

export default function Home() {

  // FAQ state management
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  // Smile Gallery state management
  const [smileGalleryData, setSmileGalleryData] = useState<SmileGalleryCardData[]>([]);
  const [galleryLoading, setGalleryLoading] = useState(true);

  // Services state management
  const [servicesData, setServicesData] = useState<ServiceCardData[]>([]);
  const [servicesLoading, setServicesLoading] = useState(true);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // Scroll to hero form function
  const scrollToHeroForm = () => {
    const heroSection = document.getElementById('hero-form');
    if (heroSection) {
      heroSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Consistent Call Back Button Component
  const CallBackButton = ({ children, className = "", onClick }: {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
  }) => (
    <button
      className={`bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold hover:shadow-lg hover:scale-105 border-2 border-white ${className}`}
      style={{ fontFamily: 'DM Sans, sans-serif' }}
      onClick={onClick || scrollToHeroForm}
    >
      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
      </svg>
      {children}
    </button>
  );

  // FAQ data
  const faqData = [
    {
      question: "Do you offer Invisalign in Katpadi?",
      answer: "Yes, we offer Invisalign treatment at our Katpadi clinic. Our experienced orthodontists provide comprehensive Invisalign consultations and treatment plans tailored to your specific needs."
    },
    {
      question: "What are your clinic hours?",
      answer: "Our clinic is open Monday to Saturday from 9:00 AM to 8:00 PM, and Sunday from 10:00 AM to 6:00 PM. We also offer emergency dental services outside regular hours."
    },
    {
      question: "Do you accept dental insurance?",
      answer: "Yes, we accept most major dental insurance plans. Our staff will help you understand your coverage and maximize your benefits. We also offer flexible payment options for treatments not covered by insurance."
    },
    {
      question: "How often should I visit the dentist?",
      answer: "We recommend visiting the dentist every 6 months for regular check-ups and cleanings. However, some patients may need more frequent visits based on their oral health condition."
    },
    {
      question: "Do you provide emergency dental services?",
      answer: "Yes, we provide emergency dental services for urgent dental problems. If you have a dental emergency, please call our emergency contact number: **********."
    }
  ];

  // Sequential icon highlighting for Why Choose section
  const whyChooseData = [
    { text: "Trusted dental care in Katpadi, Vellore", shortText: "Trusted Care" },
    { text: "Gentle treatments — root canals, braces, implants & more", shortText: "Gentle Treatments" },
    { text: "Painless procedures using advanced dental technology", shortText: "Painless Procedures" },
    { text: "Same-day dental appointments with zero wait hassle", shortText: "Same-Day Appointments" },
    { text: "Honest, upfront pricing — no hidden fees ever", shortText: "Honest Pricing" },
    { text: "Experienced dentist: Dr. Rockson Samuel (Implantologist)", shortText: "Expert Dentist" },
    { text: "Clean, modern dental clinic near Katpadi Railway Station", shortText: "Modern Clinic" },
    { text: "Great with kids, seniors & first-time patients", shortText: "Great with Kids" }
  ];

  const [currentIconIndex, setCurrentIconIndex] = useState(0);

  // Reviews data
  const reviewsData = [
    {
      id: 1,
      rating: 5,
      text: "Excellent service! Dr. Rockson is very professional and gentle. I had my root canal done here and it was completely painless. The clinic is very clean and modern. Highly recommended for anyone looking for quality dental care in Vellore.",
      author: "Rajesh Kumar",
      timeAgo: "2 weeks ago",
      initial: "R"
    },
    {
      id: 2,
      rating: 5,
      text: "Amazing experience! I was scared of dental treatments but Dr. Rockson made me feel so comfortable. The pricing is very transparent with no hidden charges. Got my teeth cleaning done and the results are fantastic. Will definitely come back!",
      author: "Priya Sharma",
      timeAgo: "1 month ago",
      initial: "P"
    },
    {
      id: 3,
      rating: 5,
      text: "Best dental clinic in Vellore! Dr. Rockson is extremely skilled and caring. I got my braces here and the treatment was excellent. The staff is very friendly and the clinic is always clean. Highly recommend to everyone!",
      author: "Arun Patel",
      timeAgo: "3 weeks ago",
      initial: "A"
    },
    {
      id: 4,
      rating: 5,
      text: "Outstanding dental care! I had dental implants done and the results are perfect. Dr. Rockson explained everything clearly and the procedure was painless. The clinic uses latest technology. Very satisfied with the service!",
      author: "Meera Reddy",
      timeAgo: "1 week ago",
      initial: "M"
    }
  ];

  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIconIndex((prevIndex) =>
        (prevIndex + 1) % whyChooseData.length
      );
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, [whyChooseData.length]);

  // Auto-scroll for reviews
  useEffect(() => {
    const reviewInterval = setInterval(() => {
      setCurrentReviewIndex((prevIndex) => {
        const maxIndex = Math.floor((reviewsData.length - 1) / 2) * 2;
        return prevIndex >= maxIndex ? 0 : prevIndex + 2;
      });
    }, 5000); // Change every 5 seconds

    return () => clearInterval(reviewInterval);
  }, [reviewsData.length]);

  // Fetch smile gallery data
  useEffect(() => {
    const fetchGalleryData = async () => {
      try {
        setGalleryLoading(true);
        const data = await getHomepageSmileGallery();
        setSmileGalleryData(data);
      } catch (error) {
        console.error('Error fetching smile gallery data:', error);
      } finally {
        setGalleryLoading(false);
      }
    };

    fetchGalleryData();
  }, []);

  // Fetch services data
  useEffect(() => {
    const fetchServicesData = async () => {
      try {
        setServicesLoading(true);
        const data = await getHomepageServices();
        setServicesData(data);
      } catch (error) {
        console.error('Error fetching services data:', error);
      } finally {
        setServicesLoading(false);
      }
    };

    fetchServicesData();
  }, []);



  return (
    <div className="min-h-screen">
      {/* Hero Section with Gradient Background */}
      <section
        id="hero-form"
        className="relative py-8 md:py-10 lg:py-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center"
        style={{
          background: 'linear-gradient(to bottom, #E53274 0%, #E53274 70%, #575C8D 100%)'
        }}
      >
        <div className="max-w-7xl mx-auto w-full">
          {/* Mobile Layout - Stacked */}
          <div className="block md:hidden">
            {/* Mobile Content */}
            <div className="text-[#FDFDFD] text-center mb-8">
              <h1 className="text-3xl font-bold leading-tight mb-4" style={{ fontFamily: 'DM Sans', fontWeight: 700 }}>
                Looking for a trusted dental clinic in Katpadi, Vellore?
              </h1>

              <div className="space-y-3 text-base max-w-lg mx-auto" style={{ fontFamily: 'Inter', fontWeight: 500 }}>
                <p style={{ fontWeight: 500 }}>
                  Book a same-day appointment with zero pain and no hidden fees.
                </p>
                <p style={{ fontWeight: 500 }}>
                  Indira Dental Clinic is your go-to clinic in Katpadi for root canals, braces, and cleanings — all done gently and transparently.
                </p>
              </div>
            </div>

            {/* Mobile Form */}
            <div className="flex justify-center w-full">
              <div className="w-full max-w-sm">
                <CallBackForm variant="hero" />
              </div>
            </div>
          </div>

          {/* Tablet and Desktop Layout - Side by Side */}
          <div className="hidden md:grid md:grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 lg:gap-12 items-center">
            {/* Left Content */}
            <div className="text-[#FDFDFD] order-2 md:order-1 lg:order-1 text-center md:text-center lg:text-left">
              <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-4 md:mb-5 lg:mb-6" style={{ fontFamily: 'DM Sans', fontWeight: 700 }}>
                Looking for a trusted dental clinic in Katpadi, Vellore?
              </h1>

              <div className="space-y-3 md:space-y-3 lg:space-y-4 text-base md:text-lg lg:text-xl max-w-2xl md:mx-auto lg:mx-0" style={{ fontFamily: 'Inter', fontWeight: 500 }}>
                <p style={{ fontWeight: 500 }}>
                  Book a same-day appointment with zero pain and no hidden fees.
                </p>
                <p style={{ fontWeight: 500 }}>
                  Indira Dental Clinic is your go-to clinic in Katpadi for root canals, braces, and cleanings — all done gently and transparently.
                </p>
              </div>
            </div>

            {/* Right Form */}
            <div className="flex justify-center order-1 md:order-2 lg:order-2 w-full">
              <div className="w-full max-w-md md:max-w-lg lg:max-w-md">
                <CallBackForm variant="hero" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 3 Positive Cards Section */}
      <section className="py-8 md:py-12 lg:py-16 xl:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
            {/* Card 1 - You'll Feel at Ease */}
            <div
              className="rounded-xl md:rounded-2xl p-4 md:p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Dentist Icon */}
              <div className="flex justify-center mb-3 md:mb-4">
                <Image
                  src="/dentist.svg"
                  alt="Dentist"
                  width={48}
                  height={48}
                  className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                />
              </div>

              <h3
                className="text-base md:text-lg lg:text-xl font-bold mb-3 md:mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                You'll Feel at Ease
              </h3>

              <p
                className="text-xs md:text-sm lg:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                Our team explains everything gently — no stress, no surprises.
              </p>
            </div>

            {/* Card 2 - Know What You Pay */}
            <div
              className="rounded-xl md:rounded-2xl p-4 md:p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Rupee Icon */}
              <div className="flex justify-center mb-3 md:mb-4">
                <Image
                  src="/rupee.svg"
                  alt="Rupee"
                  width={48}
                  height={48}
                  className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                />
              </div>

              <h3
                className="text-base md:text-lg lg:text-xl font-bold mb-3 md:mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                Know What You Pay
              </h3>

              <p
                className="text-xs md:text-sm lg:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                No hidden charges, clear options, and affordable treatment plans.
              </p>
            </div>

            {/* Card 3 - Pain? Not Here */}
            <div
              className="rounded-xl md:rounded-2xl p-4 md:p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-[#C7205D] cursor-pointer"
              style={{
                backgroundColor: '#FFF5F8',
                borderColor: '#E53274'
              }}
            >
              {/* Smile Icon */}
              <div className="flex justify-center mb-3 md:mb-4">
                <Image
                  src="/smile.svg"
                  alt="Smile"
                  width={48}
                  height={48}
                  className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                />
              </div>

              <h3
                className="text-base md:text-lg lg:text-xl font-bold mb-3 md:mb-4 text-gray-800"
                style={{ fontFamily: 'DM Sans', fontWeight: 700 }}
              >
                Pain? Not Here
              </h3>

              <p
                className="text-xs md:text-sm lg:text-base text-gray-600 leading-relaxed"
                style={{ fontFamily: 'Inter', fontWeight: 400 }}
              >
                From cleanings to root canals — comfort is our #1 focus.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center md:items-end">
            {/* Left Content */}
            <div className="text-center md:text-left">
              <h2
                className="text-base md:text-lg font-medium mb-4"
                style={{
                  fontFamily: 'DM Sans',
                  fontWeight: 600,
                  color: '#575C8D'
                }}
              >
                WHAT WE DO
              </h2>

              <h3
                className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6"
                style={{
                  fontFamily: 'DM Sans',
                  fontWeight: 700,
                  color: '#E53274'
                }}
              >
                We Deliver Pain-Free Dental Care
              </h3>

              <p
                className="text-base md:text-lg leading-relaxed mb-8 max-w-2xl md:mx-auto lg:mx-0"
                style={{
                  fontFamily: 'Inter',
                  fontWeight: 400,
                  color: '#2b2b2b'
                }}
              >
                We focus on comfort-first dental care. Whether it's your first dental visit, your root canal treatment, or a cosmetic smile upgrade — we use advanced tools and gentle techniques so every patient leaves with confidence.
              </p>

              {/* Get A Call Back Button */}
              <div className="flex justify-center md:justify-center lg:justify-start">
                <CallBackButton>
                  Get A Call Back
                </CallBackButton>
              </div>
            </div>

            {/* Right Image */}
            <div className="flex justify-center lg:justify-end h-full">
              <Image
                src="/what%20we%20do%20doctor.png"
                alt="Professional dental team at Indira Dental Clinic"
                width={800}
                height={600}
                className="w-full max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl h-auto object-cover object-bottom"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FFF5F8' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Our Services
            </h2>
            <p className="text-base md:text-lg lg:text-xl max-w-3xl mx-auto px-4"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b',
                 fontWeight: '400'
               }}>
              We offer complete dental care in Vellore — all painless, clearly priced, and tailored to your needs, right here at our Katpadi clinic.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
            {servicesLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="p-4 md:p-6 lg:p-8 rounded-lg animate-pulse"
                     style={{
                       backgroundColor: '#FDFDFD',
                       boxShadow: '10px 10px 4px 0px #00000040'
                     }}>
                  <div className="flex justify-center mb-3 md:mb-4">
                    <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full bg-gray-200"
                         style={{ borderColor: '#93D214' }}>
                    </div>
                  </div>
                  <div className="h-5 md:h-6 bg-gray-200 rounded mb-2 md:mb-3"></div>
                  <div className="h-3 md:h-4 bg-gray-200 rounded"></div>
                </div>
              ))
            ) : (
              // Dynamic service cards
              servicesData.map((service) => (
                <Link key={service.id} href={`/services/${service.slug}`}>
                  <div className="p-4 md:p-6 lg:p-8 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer group"
                       style={{
                         backgroundColor: '#FDFDFD',
                         boxShadow: '10px 10px 4px 0px #00000040'
                       }}>
                    <div className="flex justify-center mb-3 md:mb-4">
                      <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full flex items-center justify-center border-3 md:border-4 transition-all duration-300 group-hover:border-[#E53274]"
                           style={{ borderColor: '#93D214' }}>
                        <Image
                          src={service.icon}
                          alt={`${service.title} service icon`}
                          width={32}
                          height={32}
                          className="w-5 h-5 md:w-6 md:h-6 lg:w-8 lg:h-8"
                        />
                      </div>
                    </div>
                    <h3 className="text-base md:text-lg lg:text-xl font-bold text-center mb-2 md:mb-3"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#2b2b2b'
                        }}>
                      {service.title}
                    </h3>
                    <p className="text-xs md:text-sm lg:text-base text-center"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b',
                         fontWeight: '400'
                       }}>
                      {service.excerpt}
                    </p>
                  </div>
                </Link>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Meet Your Dentist Section */}
      <section className="py-12 md:py-16 lg:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Meet Your Dentist
            </h2>
          </div>

          {/* Dentist Content */}
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center">
            {/* Left - Dentist Image */}
            <div className="flex justify-center order-1 md:order-1 lg:order-1 lg:justify-start">
              <Image
                src="/rockson.png"
                alt="Dr. Rockson Samuel - Dentist at Indira Dental Clinic"
                width={500}
                height={600}
                className="w-full max-w-sm md:max-w-md lg:max-w-lg h-auto object-contain"
                priority
              />
            </div>

            {/* Right - Dentist Info with Green Line */}
            <div className="relative order-2 md:order-2 lg:order-2 text-left md:text-center lg:text-left">
              {/* Green Vertical Line */}
              <div className="absolute left-0 top-0 bottom-0 w-1 hidden lg:block"
                   style={{ backgroundColor: '#93D214' }}>
              </div>

              {/* Content with Left Padding for Line */}
              <div className="lg:pl-8">
                <h3 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-6"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#E53274'
                    }}>
                  Meet<br />
                  Dr. Rockson<br />
                  Samuel
                </h3>

                <div className="mb-8">
                  <p className="text-base md:text-lg lg:text-xl mb-2"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    BDS, MDS – Implantologist
                  </p>
                  <p className="text-base md:text-lg lg:text-xl mb-2"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    Invisalign-certified
                  </p>
                  <p className="text-base md:text-lg lg:text-xl"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '500'
                     }}>
                    Languages: Tamil | English | Hindi
                  </p>
                </div>

                {/* Call Back Button */}
                <div className="flex justify-start md:justify-center lg:justify-start">
                  <CallBackButton>
                    Get A Call Back
                  </CallBackButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Indira Dental Clinic Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Why Choose Indira Dental Clinic
            </h2>
          </div>

          {/* Content Layout */}
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left - Sequential Icon Highlighting Circle */}
            <div className="relative h-64 sm:h-80 md:h-96 lg:h-[600px] flex items-center justify-center">
              {/* Main Circle - Responsive */}
              <div className="w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[500px] lg:h-[500px] rounded-full relative border-4"
                   style={{ backgroundColor: '#FFF5F8', borderColor: '#E53274' }}>

                {/* Static Icons on Border - Mobile (w-64 h-64) */}
                <div className="absolute inset-0 sm:hidden">
                  {whyChooseData.map((item, index) => {
                    // Start from top of circle and move clockwise
                    const angle = (index * 360) / whyChooseData.length;
                    const isActive = currentIconIndex === index;
                    const radius = 128; // For 256px circle

                    return (
                      <div
                        key={index}
                        className="absolute w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-500"
                        style={{
                          backgroundColor: '#FDFDFD',
                          borderColor: '#93D214',
                          transform: `rotate(${angle}deg) translateY(-${radius}px) rotate(-${angle}deg)`,
                          left: '50%',
                          top: '50%',
                          marginLeft: '-24px',
                          marginTop: '-24px',
                          boxShadow: isActive ? '0 0 20px rgba(229, 50, 116, 0.5)' : 'none',
                          scale: isActive ? '1.1' : '1'
                        }}
                      >
                        <Image
                          src="/services-icon.svg"
                          alt="Service icon"
                          width={20}
                          height={20}
                          className="w-5 h-5"
                        />
                      </div>
                    );
                  })}
                </div>

                {/* Static Icons on Border - Small (w-80 h-80) */}
                <div className="absolute inset-0 hidden sm:block md:hidden">
                  {whyChooseData.map((item, index) => {
                    // Start from top of circle and move clockwise
                    const angle = (index * 360) / whyChooseData.length;
                    const isActive = currentIconIndex === index;
                    const radius = 160; // For 320px circle

                    return (
                      <div
                        key={index}
                        className="absolute w-14 h-14 rounded-full flex items-center justify-center border-3 transition-all duration-500"
                        style={{
                          backgroundColor: '#FDFDFD',
                          borderColor: '#93D214',
                          transform: `rotate(${angle}deg) translateY(-${radius}px) rotate(-${angle}deg)`,
                          left: '50%',
                          top: '50%',
                          marginLeft: '-28px',
                          marginTop: '-28px',
                          boxShadow: isActive ? '0 0 20px rgba(229, 50, 116, 0.5)' : 'none',
                          scale: isActive ? '1.1' : '1'
                        }}
                      >
                        <Image
                          src="/services-icon.svg"
                          alt="Service icon"
                          width={24}
                          height={24}
                          className="w-6 h-6"
                        />
                      </div>
                    );
                  })}
                </div>

                {/* Static Icons on Border - Medium (w-96 h-96) */}
                <div className="absolute inset-0 hidden md:block lg:hidden">
                  {whyChooseData.map((item, index) => {
                    // Start from top of circle and move clockwise
                    const angle = (index * 360) / whyChooseData.length;
                    const isActive = currentIconIndex === index;
                    const radius = 192; // For 384px circle

                    return (
                      <div
                        key={index}
                        className="absolute w-16 h-16 rounded-full flex items-center justify-center border-4 transition-all duration-500"
                        style={{
                          backgroundColor: '#FDFDFD',
                          borderColor: '#93D214',
                          transform: `rotate(${angle}deg) translateY(-${radius}px) rotate(-${angle}deg)`,
                          left: '50%',
                          top: '50%',
                          marginLeft: '-32px',
                          marginTop: '-32px',
                          boxShadow: isActive ? '0 0 20px rgba(229, 50, 116, 0.5)' : 'none',
                          scale: isActive ? '1.1' : '1'
                        }}
                      >
                        <Image
                          src="/services-icon.svg"
                          alt="Service icon"
                          width={28}
                          height={28}
                          className="w-7 h-7"
                        />
                      </div>
                    );
                  })}
                </div>

                {/* Static Icons on Border - Large (w-[500px] h-[500px]) */}
                <div className="absolute inset-0 hidden lg:block">
                  {whyChooseData.map((item, index) => {
                    // Start from top of circle and move clockwise
                    const angle = (index * 360) / whyChooseData.length;
                    const isActive = currentIconIndex === index;
                    const radius = 250; // For 500px circle

                    return (
                      <div
                        key={index}
                        className="absolute w-16 h-16 rounded-full flex items-center justify-center border-4 transition-all duration-500"
                        style={{
                          backgroundColor: '#FDFDFD',
                          borderColor: '#93D214',
                          transform: `rotate(${angle}deg) translateY(-${radius}px) rotate(-${angle}deg)`,
                          left: '50%',
                          top: '50%',
                          marginLeft: '-32px',
                          marginTop: '-32px',
                          boxShadow: isActive ? '0 0 20px rgba(229, 50, 116, 0.5)' : 'none',
                          scale: isActive ? '1.1' : '1'
                        }}
                      >
                        <Image
                          src="/services-icon.svg"
                          alt="Service icon"
                          width={28}
                          height={28}
                          className="w-7 h-7"
                        />
                      </div>
                    );
                  })}
                </div>

                {/* Center Content - Dynamic Text */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center px-8 max-w-xs">
                    <div className="transition-all duration-500 ease-in-out">
                      <h3 className="text-xl lg:text-2xl font-bold mb-4"
                          style={{
                            fontFamily: 'DM Sans, sans-serif',
                            color: '#E53274'
                          }}>
                        Why Choose Us?
                      </h3>
                      <p className="text-sm lg:text-base font-semibold leading-relaxed"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           color: '#2b2b2b',
                           fontWeight: '600'
                         }}>
                        {whyChooseData[currentIconIndex].shortText}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right - Features List */}
            <div className="space-y-4">
              {/* Feature 1 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Trusted dental care in Katpadi, Vellore
                </p>
              </div>

              {/* Feature 2 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Gentle treatments — root canals, braces, implants & more
                </p>
              </div>

              {/* Feature 3 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Painless procedures using advanced dental technology
                </p>
              </div>

              {/* Feature 4 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Same-day dental appointments with zero wait hassle
                </p>
              </div>

              {/* Feature 5 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Honest, upfront pricing — no hidden fees ever
                </p>
              </div>

              {/* Feature 6 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Experienced dentist: Dr. Rockson Samuel (Implantologist)
                </p>
              </div>

              {/* Feature 7 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Clean, modern dental clinic near Katpadi Railway Station
                </p>
              </div>

              {/* Feature 8 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Great with kids, seniors & first-time patients
                </p>
              </div>

              {/* Feature 9 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Open all 7 days for your convenience
                </p>
              </div>

              {/* Feature 10 */}
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#2b2b2b',
                     fontWeight: '400'
                   }}>
                  Rated 5★ on Google by happy patients in Vellore
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Google Reviews Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FFF5F8' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              5★ Rated by 80+ Happy Patients
            </h2>
            <p className="text-lg md:text-xl max-w-3xl mx-auto"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b',
                 fontWeight: '400'
               }}>
              Looking for a trusted dentist near Vellore?<br />
              Hear what our real patients say about their painless treatments and transparent pricing.
            </p>
          </div>

          {/* Reviews Slider */}
          <div className="relative">
            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {/* Display current pair of reviews */}
              {reviewsData.slice(currentReviewIndex, currentReviewIndex + 2).map((review) => (
                <div
                  key={review.id}
                  className="bg-white rounded-2xl p-8 shadow-lg cursor-pointer hover:shadow-xl transition-shadow duration-300"
                  onClick={() => window.open('https://maps.app.goo.gl/Y9N7VHf57q58ha9GA', '_blank')}
                >
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400 text-xl">
                      {'★'.repeat(review.rating)}
                    </div>
                    <span className="ml-2 text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                      {review.rating}.0
                    </span>
                  </div>

                  <p className="text-base leading-relaxed mb-6"
                     style={{
                       fontFamily: 'Inter, sans-serif',
                       color: '#2b2b2b',
                       fontWeight: '400'
                     }}>
                    "{review.text}"
                  </p>

                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                      <span className="text-pink-600 font-semibold text-lg">{review.initial}</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="font-semibold text-gray-800" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                        {review.author}
                      </h4>
                      <p className="text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                        {review.timeAgo}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Slider Dots */}
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: Math.ceil(reviewsData.length / 2) }).map((_, index) => (
                <button
                  key={index}
                  className="w-3 h-3 rounded-full transition-colors duration-300"
                  style={{
                    backgroundColor: index * 2 === currentReviewIndex ? '#93D214' : '#d1d5db'
                  }}
                  onClick={() => setCurrentReviewIndex(index * 2)}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Smile Gallery Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Real Smiles. Real Confidence.
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b',
                 fontWeight: '400'
               }}>
              See how patients just like you transformed their smiles at our Katpadi dental clinic.<br />
              From braces to smile makeovers, we create pain-free results you can trust.
            </p>
          </div>

          {/* Loading State */}
          {galleryLoading && (
            <div className="flex justify-center items-center py-16">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274] mb-4"></div>
                <p className="text-lg font-semibold text-[#E53274]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  Loading Smile Gallery...
                </p>
              </div>
            </div>
          )}

          {/* Gallery Grid */}
          {!galleryLoading && smileGalleryData.length > 0 && (
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              {smileGalleryData.map((item) => (
                <SmileGalleryCard key={item.id} data={item} />
              ))}
            </div>
          )}

          {/* No Data State */}
          {!galleryLoading && smileGalleryData.length === 0 && (
            <div className="text-center py-16">
              <p className="text-lg text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                No smile gallery posts available at the moment. Check back soon for amazing transformations!
              </p>
            </div>
          )}

          {/* View Smile Gallery Button */}
          <div className="text-center">
            <Link href="/smile-gallery">
              <button className="bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold mx-auto hover:shadow-lg hover:scale-105"
                      style={{ fontFamily: 'DM Sans, sans-serif' }}>
                View Smile Gallery
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-12 md:py-16 lg:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F1F2F8' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold px-4"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Ready for a Pain-Free Dental Visit in Katpadi?
            </h2>
          </div>

          {/* Contact Content Grid */}
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 lg:gap-8 max-w-6xl mx-auto">
            {/* Left Side - Contact Form */}
            <div className="flex justify-center">
              <CallBackForm variant="contact" />
            </div>

            {/* Right Side - Contact Info and Map */}
            <div className="space-y-8">
              {/* Contact Information */}
              <div className="space-y-6">
                {/* Address */}
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="w-6 h-6" style={{ color: '#2b2b2b' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg mb-1"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#2b2b2b'
                        }}>
                      Address:
                    </h4>
                    <p className="text-base leading-relaxed"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b'
                       }}>
                      3rd Floor, 54, Katpadi Main Rd, Suthanthira Ponvizha Nagar, Gandhi Nagar, Vellore, Tamil Nadu 632006
                    </p>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    <svg className="w-6 h-6" style={{ color: '#2b2b2b' }} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                  </div>
                  <div>
                    <p className="text-lg font-semibold"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b'
                       }}>
                      **********
                    </p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    <svg className="w-6 h-6" style={{ color: '#2b2b2b' }} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                    </svg>
                  </div>
                  <div>
                    <p className="text-lg font-semibold"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b'
                       }}>
                      <EMAIL>
                    </p>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div className="pt-4">
                  <h4 className="font-semibold text-lg mb-3"
                      style={{
                        fontFamily: 'DM Sans, sans-serif',
                        color: '#2b2b2b'
                      }}>
                    Emergency Contact No.
                  </h4>
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <svg className="w-6 h-6" style={{ color: '#2b2b2b' }} fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="text-lg font-semibold"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           color: '#2b2b2b'
                         }}>
                        **********
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Google Maps */}
              <div className="rounded-2xl overflow-hidden shadow-lg h-80">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3886.8234567890123!2d79.13456789012345!3d12.916789012345678!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTLCsDU1JzAwLjQiTiA3OcKwMDgnMDQuNCJF!5e0!3m2!1sen!2sin!4v1234567890123!5m2!1sen!2sin&q=Katpadi+Main+Road+Vellore+Tamil+Nadu"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen={true}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Indira Dental Clinic - Katpadi, Vellore"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dental FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-4">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Dental FAQ's
            </h2>
          </div>

          {/* Subtitle */}
          <div className="text-center mb-12">
            <p className="text-base md:text-lg max-w-3xl mx-auto"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b',
                 fontWeight: '400'
               }}>
              Fill out the call back form and our team will get back to you as soon as possible — your comfort and dental health are always our top priorities.
            </p>
          </div>

          {/* FAQ Items */}
          <div className="space-y-4">
            {faqData.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg p-6"
                   style={{
                     boxShadow: '5px 5px 4px 0px #00000040'
                   }}>
                <div className="flex items-center justify-between cursor-pointer"
                     onClick={() => toggleFAQ(index)}>
                  <h3 className="text-lg md:text-xl font-semibold"
                      style={{
                        fontFamily: 'DM Sans, sans-serif',
                        color: '#2b2b2b'
                      }}>
                    {faq.question}
                  </h3>
                  <div className="flex-shrink-0 ml-4">
                    <svg className={`w-6 h-6 transform transition-transform duration-200 ${
                           openFAQ === index ? 'rotate-180' : ''
                         }`}
                         style={{ color: '#93D214' }}
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>

                {/* FAQ Answer */}
                {openFAQ === index && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-base leading-relaxed"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b',
                         fontWeight: '400'
                       }}>
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
