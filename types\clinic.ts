// WordPress clinic post structure
export interface ClinicPost {
  id: number;
  date: string;
  date_gmt: string;
  guid: {
    rendered: string;
  };
  modified: string;
  modified_gmt: string;
  slug: string;
  status: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    protected: boolean;
  };
  excerpt: {
    rendered: string;
    protected: boolean;
  };
  author: number;
  featured_media: number;
  comment_status: string;
  ping_status: string;
  sticky: boolean;
  template: string;
  format: string;
  meta: any[];
  categories: number[];
  tags: number[];
  acf?: ClinicACF;
  _links: any;
}

// ACF (Advanced Custom Fields) structure for clinic
export interface ClinicACF {
  // Clinic location (Google Maps field)
  clinic_location?: {
    address: string;
    lat: number;
    lng: number;
    zoom: number;
    place_id: string;
    name: string;
    street_number: number;
    street_name: string;
    street_name_short: string;
    city: string;
    state: string;
    state_short: string;
    post_code: number;
    country: string;
    country_short: string;
  };

  // Clinic images
  clinic_image_1?: number;
  clinic_image_2?: number;
  clinic_image_3?: number | string;
  clinic_image_4?: number | string;
  clinic_image_5?: number | string;
  
  // Business hours (individual day fields)
  monday_open?: string;
  monday_close?: string;
  monday_closed?: boolean;
  tuesday_open?: string;
  tuesday_close?: string;
  tuesday_closed?: boolean;
  wednesday_open?: string;
  wednesday_close?: string;
  wednesday_closed?: boolean;
  thursday_open?: string;
  thursday_close?: string;
  thursday_closed?: boolean;
  friday_open?: string;
  friday_close?: string;
  friday_closed?: boolean;
  saturday_open?: string;
  saturday_close?: string;
  saturday_closed?: boolean;
  sunday_open?: string;
  sunday_close?: string;
  sunday_closed?: boolean;
  
  // Processed business hours
  businessHours?: BusinessHour[];
  
  // Reviews (individual review fields)
  review_1_name?: string;
  review_1_text?: string;
  review_1_rating?: string;
  review_1_date?: string;
  review_2_name?: string;
  review_2_text?: string;
  review_2_rating?: string;
  review_2_date?: string;
  review_3_name?: string;
  review_3_text?: string;
  review_3_rating?: string;
  review_3_date?: string;
  review_4_name?: string;
  review_4_text?: string;
  review_4_rating?: string;
  review_4_date?: string;
  review_5_name?: string;
  review_5_text?: string;
  review_5_rating?: string;
  review_5_date?: string;
  review_6_name?: string;
  review_6_text?: string;
  review_6_rating?: string;
  review_6_date?: string;
  review_7_name?: string;
  review_7_text?: string;
  review_7_rating?: string;
  review_7_date?: string;
  review_8_name?: string;
  review_8_text?: string;
  review_8_rating?: string;
  review_8_date?: string;
  review_9_name?: string;
  review_9_text?: string;
  review_9_rating?: string;
  review_9_date?: string;
  review_10_name?: string;
  review_10_text?: string;
  review_10_rating?: string;
  review_10_date?: string;
  
  // Processed reviews
  reviews?: Review[];
  
  // Facilities (individual facility fields)
  facility_1?: string;
  facility_2?: string;
  facility_3?: string;
  facility_4?: string;
  facility_5?: string;
  facility_6?: string;
  facility_7?: string;
  facility_8?: string;
  facility_9?: string;
  facility_10?: string;
  facility_11?: string;
  facility_12?: string;
  facility_13?: string;
  facility_14?: string;
  facility_15?: string;
  facility_16?: string;
  facility_17?: string;
  facility_18?: string;
  facility_19?: string;
  facility_20?: string;
  
  // Processed facilities
  facilities?: string[];
  
  // Services offered (individual service fields)
  service_1?: string;
  service_2?: string;
  service_3?: string;
  service_4?: string;
  service_5?: string;
  service_6?: string;
  service_7?: string;
  service_8?: string;
  service_9?: string;
  service_10?: string;
  service_11?: string;
  service_12?: string;
  service_13?: string;
  service_14?: string;
  service_15?: string;
  service_16?: string;
  service_17?: string;
  service_18?: string;
  service_19?: string;
  service_20?: string;
  
  // Processed services
  servicesOffered?: string[];
  
  // Additional fields
  google_maps_url?: string;
  overall_rating?: string;
  total_reviews?: string;
  clinic_images?: any[];
}

// Business hour structure
export interface BusinessHour {
  day: string;
  open: string;
  close: string;
  closed: boolean;
}

// Review structure
export interface Review {
  name: string;
  text: string;
  rating: number;
  date: string;
}
