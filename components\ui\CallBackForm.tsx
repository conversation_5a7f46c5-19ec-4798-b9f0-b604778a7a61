'use client';

import { useEffect, useRef, useState } from 'react';

interface CallBackFormProps {
  variant?: 'hero' | 'contact';
  className?: string;
}

export default function CallBackForm({ variant = 'hero', className = '' }: CallBackFormProps) {
  const isHeroVariant = variant === 'hero';
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeHeight, setIframeHeight] = useState<string>('600px');

  // Container styling based on variant - minimal padding for tight fit
  const containerClasses = isHeroVariant
    ? "bg-[#FDFDFD] rounded-2xl shadow-xl w-full max-w-md p-1"
    : "bg-[#FDFDFD] rounded-2xl shadow-lg border-2 border-[#93D214] w-full max-w-lg p-1";

  // Responsive height calculation
  useEffect(() => {
    const calculateHeight = () => {
      const screenHeight = window.innerHeight;
      const screenWidth = window.innerWidth;

      // Mobile devices - increased heights for full scrolling
      if (screenWidth < 768) {
        setIframeHeight(isHeroVariant ? '600px' : '650px');
      }
      // Tablet devices
      else if (screenWidth < 1024) {
        setIframeHeight(isHeroVariant ? '750px' : '800px');
      }
      // Desktop devices
      else {
        setIframeHeight(isHeroVariant ? '800px' : '850px');
      }
    };

    // Calculate on mount
    calculateHeight();

    // Recalculate on resize
    window.addEventListener('resize', calculateHeight);

    return () => window.removeEventListener('resize', calculateHeight);
  }, [isHeroVariant]);

  // Handle iframe load to adjust height dynamically
  const handleIframeLoad = () => {
    if (iframeRef.current) {
      try {
        // Try to access iframe content height (may be blocked by CORS)
        const iframeDocument = iframeRef.current.contentDocument;
        if (iframeDocument) {
          const contentHeight = iframeDocument.body.scrollHeight;
          if (contentHeight > 0) {
            // Add extra padding to ensure full scrollability
            const adjustedHeight = contentHeight + 50;
            setIframeHeight(`${Math.min(adjustedHeight, 1000)}px`);
          }
        }
      } catch (error) {
        // CORS blocked - use responsive defaults with extra height for scrolling
        console.log('Using responsive height due to CORS restrictions');
        // Ensure enough height for full form scrolling
        const screenWidth = window.innerWidth;
        if (screenWidth < 768) {
          setIframeHeight(isHeroVariant ? '650px' : '700px');
        } else if (screenWidth < 1024) {
          setIframeHeight(isHeroVariant ? '800px' : '850px');
        } else {
          setIframeHeight(isHeroVariant ? '850px' : '900px');
        }
      }
    }
  };

  return (
    <div className={`${containerClasses} ${className} flex flex-col`}
         style={{
           overflow: 'hidden',
           position: 'relative',
           margin: '0',
           padding: '4px',
         }}>
      <div
        style={{
          height: iframeHeight,
          overflow: 'auto',
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* IE and Edge */
          WebkitOverflowScrolling: 'touch', /* iOS smooth scrolling */
          scrollBehavior: 'smooth',
          overscrollBehavior: 'contain',
          margin: '0',
          padding: '0',
          borderRadius: '0.75rem',
        }}
        className="scrollbar-hide form-scroll-container"
      >
        <iframe
          ref={iframeRef}
          src="https://app.dentalclinicinvellore.in/form/"
          width="100%"
          height="100%"
          style={{
            border: 'none',
            borderRadius: '0.75rem',
            minHeight: isHeroVariant ? '700px' : '750px',
            height: '100%',
            display: 'block',
            margin: '0',
            padding: '0',
            width: '100%',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            backgroundColor: 'transparent',
          }}
          title="Dental Clinic Contact Form"
          loading="lazy"
          sandbox="allow-forms allow-scripts allow-same-origin allow-top-navigation"
          scrolling="yes"
          className="scrollbar-hide"
          onLoad={handleIframeLoad}
        />
      </div>
    </div>
  );
}
