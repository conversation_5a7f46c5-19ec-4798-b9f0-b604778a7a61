import Link from 'next/link';

interface BookAppointmentButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'white';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
  fullWidth?: boolean;
}

export default function BookAppointmentButton({ 
  variant = 'primary', 
  size = 'md', 
  className = '',
  children = 'Book Appointment',
  fullWidth = false
}: BookAppointmentButtonProps) {
  
  // Base styles - added flex and items-center for proper icon alignment
  const baseStyles = "inline-flex items-center justify-center font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 text-center border-2";

  // Size variants
  const sizeStyles = {
    sm: "px-4 py-2 text-sm rounded-md",
    md: "px-6 py-3 text-base rounded-lg",
    lg: "px-8 py-4 text-lg rounded-lg"
  };

  // Color variants - all variants now have white borders
  const variantStyles = {
    primary: "bg-[#E53274] hover:bg-[#C7205D] text-white border-white",
    secondary: "bg-[#575C8D] hover:bg-[#4A4F7A] text-white border-white",
    outline: "border-white text-[#E53274] hover:bg-[#E53274] hover:text-white bg-transparent",
    white: "bg-white text-[#E53274] hover:bg-gray-100 border-white"
  };
  
  // Width styles
  const widthStyles = fullWidth ? "w-full" : "";
  
  const combinedStyles = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${widthStyles} ${className}`;
  
  return (
    <Link 
      href="/book-appointment"
      className={combinedStyles}
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {children}
    </Link>
  );
}
