'use client';

import { useState } from 'react';

interface NewsletterProps {
  className?: string;
}

export default function Newsletter({ className = '' }: NewsletterProps) {
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simple alert for demo purposes since user requested no newsletter functionality
    alert('Newsletter signup functionality would be implemented here');
    setEmail('');
  };

  return (
    <div className={`bg-white rounded-lg p-6 shadow-sm border border-gray-100 ${className}`}>
      {/* Newsletter Header */}
      <h3 className="text-lg font-bold mb-4" 
          style={{ 
            fontFamily: 'DM Sans, sans-serif',
            color: '#2b2b2b'
          }}>
        Newsletter
      </h3>

      {/* Newsletter Description */}
      <p className="text-sm mb-4 leading-relaxed"
         style={{
           fontFamily: 'Inter, sans-serif',
           color: '#666666'
         }}>
        Stay updated with dental health tips and clinic news.
      </p>

      {/* Newsletter Form */}
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Email Input */}
        <div className="relative">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Email for newsletter"
            className="w-full px-4 py-3 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E53274] focus:border-transparent transition-colors"
            style={{ 
              fontFamily: 'Inter, sans-serif'
            }}
            required
          />
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full py-3 px-4 text-sm font-medium text-white rounded-lg transition-colors hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-[#E53274] focus:ring-offset-2"
          style={{ 
            backgroundColor: '#4A90E2',
            fontFamily: 'DM Sans, sans-serif'
          }}
        >
          Subscribe
        </button>
      </form>
    </div>
  );
}
