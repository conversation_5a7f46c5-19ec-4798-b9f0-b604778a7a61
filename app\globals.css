@import "tailwindcss";

:root {
  --background: #fdfdfd;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile-optimized animations and touch interactions */
/* Smooth scrolling for all devices */
html {
  scroll-behavior: smooth;
}

/* Optimize touch interactions */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Force hover effects to work on all devices */
/* Override mobile hover restrictions */
.hover\:scale-105 {
  transition: transform 0.3s ease;
}

.hover\:scale-105:hover,
.hover\:scale-105:focus {
  transform: scale(1.05) !important;
}

.hover\:shadow-xl {
  transition: box-shadow 0.3s ease;
}

.hover\:shadow-xl:hover,
.hover\:shadow-xl:focus {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
}

.hover\:shadow-2xl {
  transition: box-shadow 0.3s ease;
}

.hover\:shadow-2xl:hover,
.hover\:shadow-2xl:focus {
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
}

.hover\:border-\[\#C7205D\] {
  transition: border-color 0.3s ease;
}

.hover\:border-\[\#C7205D\]:hover,
.hover\:border-\[\#C7205D\]:focus {
  border-color: #C7205D !important;
}

.group-hover\:border-\[\#E53274\] {
  transition: border-color 0.3s ease;
}

.group:hover .group-hover\:border-\[\#E53274\],
.group:focus .group-hover\:border-\[\#E53274\] {
  border-color: #E53274 !important;
}

/* Mobile-specific hover simulation */
@media (hover: none) and (pointer: coarse) {
  /* Enable touch-based hover simulation */
  .hover\:scale-105:active {
    transform: scale(1.05) !important;
  }

  .hover\:shadow-xl:active {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
  }

  .hover\:shadow-2xl:active {
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
  }

  .hover\:border-\[\#C7205D\]:active {
    border-color: #C7205D !important;
  }

  .group:active .group-hover\:border-\[\#E53274\] {
    border-color: #E53274 !important;
  }

  /* Make elements focusable for touch hover */
  .hover\:scale-105,
  .hover\:shadow-xl,
  .hover\:shadow-2xl,
  .hover\:border-\[\#C7205D\],
  .group {
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Slightly reduce scale effects on tablets */
  .hover\:scale-105:hover {
    transform: scale(1.03);
  }

  /* Tablet-specific spacing */
  .tablet-padding {
    padding: 1.5rem;
  }

  .tablet-gap {
    gap: 2rem;
  }

  /* Tablet typography */
  .tablet-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .tablet-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Tablet grid optimizations */
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Tablet button sizing */
  .tablet-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  /* Tablet form optimizations */
  .tablet-form-height {
    min-height: 600px;
    max-height: 700px;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom utilities for better mobile experience */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-friendly button press effect */
.btn-press {
  transition: transform 0.1s ease-in-out;
}

.btn-press:active {
  transform: scale(0.95);
}

/* Hide scrollbars while maintaining scrollability */
.scrollbar-hide {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.scrollbar-hide::-webkit-scrollbar-track {
  display: none !important;
}

.scrollbar-hide::-webkit-scrollbar-thumb {
  display: none !important;
}

.scrollbar-hide::-webkit-scrollbar-corner {
  display: none !important;
}

/* Smooth scrolling for form containers */
.form-scroll-container {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  max-height: 100% !important;
}

/* Ensure iframe content is fully scrollable */
.form-scroll-container iframe {
  min-height: 100% !important;
  overflow: auto !important;
}

/* Global scrollbar hiding for iframe content */
iframe {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

iframe::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Hide scrollbars in any nested content */
.scrollbar-hide * {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.scrollbar-hide *::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Specific targeting for form containers */
[class*="form"] *::-webkit-scrollbar,
[id*="form"] *::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Remove fixed header spacing since headers are now normal flow */
body {
  overflow-x: hidden;
}

/* Additional scroll padding for service pages with double sticky headers */
.service-page {
  scroll-padding-top: 160px; /* Main header (90px) + Service nav (60px) + buffer (10px) */
}

/* Offset for anchor links to account for sticky headers */
.service-page section[id] {
  scroll-margin-top: 170px; /* Main header (90px) + Service nav (60px) + buffer (20px) */
}

/* Ensure navigation links don't have underlines */
.service-page nav a {
  text-decoration: none !important;
}

.service-page nav a:hover {
  text-decoration: none !important;
}

/* Ensure sticky elements work properly on all devices */
html {
  height: 100%;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* Critical fix for sticky positioning */
html, body {
  height: 100%;
  min-height: 100%;
  overflow-y: auto; /* ensure scrolling works */
  position: relative; /* required for sticky ancestors */
}

/* Accessibility Features Styles */
:root {
  --accessibility-font-scale: 1.0;
}

.accessibility-active {
  /* High contrast mode */
  --accessibility-bg: #000000;
  --accessibility-text: #ffffff;
  --accessibility-primary: #ffff00;
  --accessibility-secondary: #00ffff;
  --accessibility-border: #ffffff;
}

.accessibility-active * {
  background-color: var(--accessibility-bg) !important;
  color: var(--accessibility-text) !important;
  border-color: var(--accessibility-border) !important;
}

.accessibility-active a,
.accessibility-active button,
.accessibility-active [role="button"] {
  background-color: var(--accessibility-primary) !important;
  color: #000000 !important;
  border: 2px solid var(--accessibility-border) !important;
}

.accessibility-active .bg-\[\#E53274\],
.accessibility-active .bg-\[\#575C8D\],
.accessibility-active .bg-\[\#93D214\] {
  background-color: var(--accessibility-primary) !important;
  color: #000000 !important;
}

/* Font scaling mode - more refined scaling */
.font-scale-active {
  --font-scale: var(--accessibility-font-scale);
}

/* Apply scaling to text elements only, not all elements */
.font-scale-active h1 {
  font-size: calc(2.25rem * var(--font-scale)) !important;
}

.font-scale-active h2 {
  font-size: calc(1.875rem * var(--font-scale)) !important;
}

.font-scale-active h3 {
  font-size: calc(1.5rem * var(--font-scale)) !important;
}

.font-scale-active h4 {
  font-size: calc(1.25rem * var(--font-scale)) !important;
}

.font-scale-active h5 {
  font-size: calc(1.125rem * var(--font-scale)) !important;
}

.font-scale-active h6 {
  font-size: calc(1rem * var(--font-scale)) !important;
}

.font-scale-active p,
.font-scale-active span,
.font-scale-active div,
.font-scale-active a,
.font-scale-active li,
.font-scale-active td,
.font-scale-active th {
  font-size: calc(1rem * var(--font-scale)) !important;
  line-height: calc(1.5 * var(--font-scale)) !important;
}

.font-scale-active button,
.font-scale-active input,
.font-scale-active textarea,
.font-scale-active label {
  font-size: calc(0.875rem * var(--font-scale)) !important;
}

/* Scale button padding more conservatively */
.font-scale-active button {
  padding: calc(0.5rem * var(--font-scale)) calc(1rem * var(--font-scale)) !important;
}

/* Keep accessibility widget reasonably sized */
.font-scale-active .accessibility-toggle {
  width: calc(60px * min(var(--font-scale), 1.2)) !important;
  height: calc(60px * min(var(--font-scale), 1.2)) !important;
  font-size: calc(24px * min(var(--font-scale), 1.2)) !important;
}

.font-scale-active .accessibility-menu {
  min-width: calc(280px * min(var(--font-scale), 1.15)) !important;
  max-width: calc(320px * min(var(--font-scale), 1.15)) !important;
}

/* Ensure small text remains readable and buttons stay usable */
.font-scale-active[style*="--accessibility-font-scale: 0.75"] button,
.font-scale-active[style*="--accessibility-font-scale: 0.75"] input,
.font-scale-active[style*="--accessibility-font-scale: 0.75"] textarea {
  font-size: max(0.7rem, calc(0.875rem * var(--font-scale))) !important;
  min-height: 32px !important;
}

/* Prevent buttons from becoming too small */
.font-scale-active button {
  min-height: 36px !important;
  min-width: 60px !important;
}

/* Focus indicators for keyboard navigation */
.accessibility-active *:focus,
.accessibility-active *:focus-visible {
  outline: 3px solid var(--accessibility-secondary) !important;
  outline-offset: 2px !important;
}

/* Accessibility toggle button styles */
.accessibility-toggle {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 9999;
  background: #E53274;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 24px;
}

.accessibility-toggle:hover {
  background: #C7205D;
  transform: scale(1.05);
}

.accessibility-toggle:focus {
  outline: 3px solid #93D214;
  outline-offset: 2px;
}

/* Accessibility menu styles */
.accessibility-menu {
  position: fixed;
  bottom: 90px;
  left: 20px;
  z-index: 9998;
  background: white;
  border: 2px solid #E53274;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  max-width: 320px;
  transform: translateY(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.accessibility-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.accessibility-menu h3 {
  margin: 0 0 12px 0;
  color: #E53274;
  font-family: 'DM Sans', sans-serif;
  font-weight: 700;
  font-size: 16px;
}

.accessibility-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  margin: 4px 0;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #2b2b2b;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.accessibility-menu button:hover {
  background: #e9ecef;
  border-color: #E53274;
}

.accessibility-menu button:focus {
  outline: 2px solid #E53274;
  outline-offset: 1px;
}

.accessibility-menu button.active {
  background: #E53274;
  color: white;
  border-color: #E53274;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #E53274;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}
