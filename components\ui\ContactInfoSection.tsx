'use client';

import Link from "next/link";

export default function ContactInfoSection() {
  return (
    <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
      <div className="max-w-7xl mx-auto">
        {/* Top Row - 3 Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Emergency Care Card */}
          <div className="bg-[#FFF5F8] border-2 border-[#E53274] rounded-2xl p-6 text-center shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <h3 className="text-xl font-bold mb-2" 
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Emergency Care
            </h3>
            <p className="text-base" 
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 400 }}>
              24/7 Emergency Dental Services Available
            </p>
          </div>

          {/* In Patient Services Card */}
          <div className="bg-[#FFF5F8] border-2 border-[#E53274] rounded-2xl p-6 text-center shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <h3 className="text-xl font-bold mb-2" 
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              In Patient Services 24/7
            </h3>
            <p className="text-base" 
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 400 }}>
              Comprehensive Care Around the Clock
            </p>
          </div>

          {/* Book Appointment Card */}
          <div className="bg-[#575C8D] border-2 border-[#575C8D] rounded-2xl p-6 text-center shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <Link href="/book-appointment" className="block">
              <h3 className="text-xl font-bold mb-2" 
                  style={{ fontFamily: 'DM Sans, sans-serif', color: '#FDFDFD' }}>
                Book Appointment
              </h3>
              <p className="text-base" 
                 style={{ fontFamily: 'Inter, sans-serif', color: '#FDFDFD', fontWeight: 400 }}>
                Schedule Your Visit Online
              </p>
            </Link>
          </div>
        </div>

        {/* Bottom Row - 3 Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Out Patient Services Card */}
          <div className="bg-[#FDFDFD] border-2 border-[#93D214] rounded-2xl p-6 shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <h3 className="text-xl font-bold mb-4" 
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Out Patient Services
            </h3>
            <div className="space-y-2">
              <p className="text-base"
                 style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 500 }}>
                Mon-Sat: 10am-8pm
              </p>
              <p className="text-base"
                 style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 500 }}>
                Sun: 10am-1:30pm
              </p>
            </div>
          </div>

          {/* Emergency 24/7 Card */}
          <div className="bg-[#FDFDFD] border-2 border-[#93D214] rounded-2xl p-6 shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 mt-1">
                <svg className="w-8 h-8" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-3" 
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Emergency 24/7
                </h3>
                <div className="space-y-2">
                  <p className="text-base"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 500 }}>
                    <a href="tel:+917010650063" className="hover:text-[#E53274] transition-colors">
                      7010650063
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Email Card */}
          <div className="bg-[#FDFDFD] border-2 border-[#93D214] rounded-2xl p-6 shadow-lg transition-all duration-300 hover:shadow-lg hover:scale-[1.02]">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 mt-1">
                <svg className="w-8 h-8" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-3" 
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Email
                </h3>
                <div className="space-y-2">
                  <p className="text-base"
                     style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: 500 }}>
                    <a href="mailto:<EMAIL>" className="hover:text-[#E53274] transition-colors">
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
