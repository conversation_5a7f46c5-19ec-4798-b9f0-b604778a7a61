'use client';

import Image from 'next/image';
import Link from 'next/link';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';

export default function AboutPage() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#FDFDFD' }}>
      {/* Hero Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            About Indira Dental Clinic
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Your trusted dental care partner in Katpadi, Vellore, providing exceptional 
            dental services with a commitment to patient comfort and satisfaction.
          </p>
        </div>
      </section>

      {/* About Content */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Content */}
            <div>
              <h2 className="text-2xl md:text-3xl font-bold mb-6" 
                  style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                Our Story
              </h2>
              <div className="space-y-4 text-base md:text-lg" 
                   style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                <p>
                  Indira Dental Clinic has been serving the Vellore community for years, 
                  providing comprehensive dental care with a focus on patient comfort and 
                  advanced treatment techniques.
                </p>
                <p>
                  Located in the heart of Katpadi, our clinic is equipped with state-of-the-art 
                  technology and staffed by experienced dental professionals who are committed 
                  to delivering the highest quality care.
                </p>
                <p>
                  We believe in transparent pricing, pain-free procedures, and building 
                  long-term relationships with our patients based on trust and excellent results.
                </p>
              </div>
            </div>

            {/* YouTube Video */}
            <div className="rounded-lg overflow-hidden shadow-lg">
              <div className="relative w-full h-64 md:h-80">
                <iframe
                  src="https://www.youtube.com/embed/cR4f7FXuSfc"
                  title="Indira Dental Clinic Video"
                  className="w-full h-full"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-12" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Our Values
          </h2>
          <div className="grid md:grid-cols-3 gap-6 md:gap-8">
            {[
              {
                title: "Patient Comfort",
                description: "We prioritize your comfort with pain-free procedures and a relaxing environment.",
                icon: "😊"
              },
              {
                title: "Quality Care",
                description: "Advanced techniques and equipment ensure the highest quality dental treatments.",
                icon: "⭐"
              },
              {
                title: "Transparent Pricing",
                description: "No hidden fees - we believe in honest, upfront pricing for all our services.",
                icon: "💰"
              }
            ].map((value, index) => (
              <div key={index} className="text-center">
                <div className="text-5xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-bold mb-3" 
                    style={{ fontFamily: 'DM Sans, sans-serif', color: '#575C8D' }}>
                  {value.title}
                </h3>
                <p style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F1F2F8' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Experience the Difference
          </h2>
          <p className="text-lg mb-8" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Join thousands of satisfied patients who trust Indira Dental Clinic for their dental care needs.
          </p>
          <BookAppointmentButton size="lg">
            Schedule Your Visit
          </BookAppointmentButton>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
