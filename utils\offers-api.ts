import { OfferPost, OfferCardData, FeaturedMediaResponse } from '@/types/offers';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Fetch offer posts from WordPress API
export async function fetchOfferPosts(): Promise<OfferPost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/offer/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch offer posts: ${response.status} ${response.statusText}`);
    }

    const posts: OfferPost[] = await response.json();
    console.log('Fetched offers data:', posts); // Debug logging
    return posts;
  } catch (error) {
    console.error('Error fetching offer posts:', error);
    return [];
  }
}

// Fetch a single offer by slug
export async function fetchOfferBySlug(slug: string): Promise<OfferPost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/offer?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch offer: ${response.status} ${response.statusText}`);
    }

    const posts: OfferPost[] = await response.json();
    return posts.length > 0 ? posts[0] : null;
  } catch (error) {
    console.error('Error fetching offer by slug:', error);
    return null;
  }
}

// Fetch featured media by ID
export async function fetchFeaturedMedia(mediaId: number): Promise<string> {
  try {
    if (!mediaId) return '';

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/media/${mediaId}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch media: ${response.status} ${response.statusText}`);
    }

    const media: FeaturedMediaResponse = await response.json();
    return media.source_url || '';
  } catch (error) {
    console.error('Error fetching featured media:', error);
    return '';
  }
}

// Convert offer posts to card data
export function convertToOfferCardData(posts: any[]): OfferCardData[] {
  return posts.filter(post => post && (post.id || post.slug)).map((post, index) => {
    console.log(`Processing offer ${index}:`, post); // Debug each offer

    return {
      id: post.id || index + 1,
      title: post.title?.rendered || post.title || post.slug || `Offer ${index + 1}`,
      excerpt: post.excerpt?.rendered
        ? post.excerpt.rendered.replace(/<[^>]*>/g, '').trim()
        : post.excerpt || 'No description available',
      slug: post.slug || `offer-${index + 1}`,
      featured_media: post.featured_media || 0,
      start_date: post.acf?.offer_start_date || '',
      end_date: post.acf?.offer_end_date || '',
      content: post.content?.rendered || post.content || '',
      acf: post.acf || {}
    };
  });
}

// Get all offers for listing page
export async function getAllOffers(): Promise<OfferCardData[]> {
  try {
    const posts = await fetchOfferPosts();
    console.log('Raw posts data:', posts); // Debug log

    if (!Array.isArray(posts)) {
      console.warn('API did not return an array:', posts);
      return [];
    }

    const convertedData = convertToOfferCardData(posts);
    console.log('Converted offers data:', convertedData); // Debug log
    return convertedData;
  } catch (error) {
    console.error('Error getting all offers:', error);
    return [];
  }
}

// Format date from YYYYMMDD to readable format
export function formatOfferDate(dateString: string): string {
  if (!dateString || dateString.length !== 8) return '';
  
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  
  const date = new Date(`${year}-${month}-${day}`);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Check if offer is currently active
export function isOfferActive(startDate: string, endDate: string): boolean {
  if (!startDate || !endDate) return false;
  
  const now = new Date();
  const start = new Date(`${startDate.substring(0, 4)}-${startDate.substring(4, 6)}-${startDate.substring(6, 8)}`);
  const end = new Date(`${endDate.substring(0, 4)}-${endDate.substring(4, 6)}-${endDate.substring(6, 8)}`);
  
  return now >= start && now <= end;
}
