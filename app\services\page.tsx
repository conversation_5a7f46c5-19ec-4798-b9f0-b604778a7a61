'use client';

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from 'next/link';
import { ServiceCardData } from "@/types/services";
import { getAllServices } from "@/utils/services-api";
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';

export default function ServicesPage() {
  // Services state management
  const [servicesData, setServicesData] = useState<ServiceCardData[]>([]);
  const [servicesLoading, setServicesLoading] = useState(true);

  // Fetch services data
  useEffect(() => {
    const fetchServicesData = async () => {
      try {
        setServicesLoading(true);
        const data = await getAllServices();
        setServicesData(data);
      } catch (error) {
        console.error('Error fetching services data:', error);
      } finally {
        setServicesLoading(false);
      }
    };

    fetchServicesData();
  }, []);

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#FDFDFD' }}>
      {/* Hero Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Our Dental Services
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Comprehensive dental care services in Vellore with expert dentists providing 
            professional treatments in a comfortable environment.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
            {servicesLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="p-4 md:p-6 lg:p-8 rounded-lg animate-pulse"
                     style={{
                       backgroundColor: '#FDFDFD',
                       boxShadow: '10px 10px 4px 0px #00000040'
                     }}>
                  <div className="flex justify-center mb-3 md:mb-4">
                    <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full bg-gray-200"
                         style={{ borderColor: '#93D214' }}>
                    </div>
                  </div>
                  <div className="h-5 md:h-6 bg-gray-200 rounded mb-2 md:mb-3"></div>
                  <div className="h-3 md:h-4 bg-gray-200 rounded"></div>
                </div>
              ))
            ) : (
              // Dynamic service cards
              servicesData.map((service) => (
                <Link key={service.id} href={`/services/${service.slug}`}>
                  <div className="p-4 md:p-6 lg:p-8 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer group"
                       style={{
                         backgroundColor: '#FDFDFD',
                         boxShadow: '10px 10px 4px 0px #00000040'
                       }}>
                    <div className="flex justify-center mb-3 md:mb-4">
                      <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full flex items-center justify-center border-3 md:border-4 transition-all duration-300 group-hover:border-[#E53274]"
                           style={{ borderColor: '#93D214' }}>
                        <Image
                          src={service.icon}
                          alt={`${service.title} service icon`}
                          width={32}
                          height={32}
                          className="w-5 h-5 md:w-6 md:h-6 lg:w-8 lg:h-8"
                        />
                      </div>
                    </div>
                    <h3 className="text-base md:text-lg lg:text-xl font-bold text-center mb-2 md:mb-3"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#2b2b2b'
                        }}>
                      {service.title}
                    </h3>
                    <p className="text-xs md:text-sm lg:text-base text-center"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b',
                         fontWeight: '400'
                       }}>
                      {service.excerpt}
                    </p>
                  </div>
                </Link>
              ))
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F1F2F8' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Ready to Schedule Your Appointment?
          </h2>
          <p className="text-lg mb-8" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Contact us today to book your consultation with our expert dental team.
          </p>
          <BookAppointmentButton size="lg" />
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
