'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { BlogPostData } from '@/types/blog';
import { getBlogPostData } from '@/utils/blog-api';
import AuthorProfile from '@/components/ui/blog/AuthorProfile';
import Categories from '@/components/ui/blog/Categories';
import ContactInfoSection from '@/components/ui/ContactInfoSection';

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [post, setPost] = useState<BlogPostData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPost = async () => {
      try {
        setLoading(true);
        const postData = await getBlogPostData(slug);
        if (postData) {
          setPost(postData);
        } else {
          setError('Blog post not found');
        }
      } catch (err) {
        console.error('Error loading blog post:', err);
        setError('Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      loadPost();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274]"></div>
          <p className="mt-4 text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
            Loading blog post...
          </p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {error || 'Blog post not found'}
          </h1>
          <Link 
            href="/blog"
            className="text-[#E53274] hover:underline"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            ← Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';

  return (
    <div className="min-h-screen">
      {/* Hero Section with Featured Image */}
      <section className="relative h-96 bg-gray-200">
        {post.featuredImage ? (
          <Image
            src={post.featuredImage}
            alt={post.title}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-[#E53274] to-[#575C8D] flex items-center justify-center">
            <i className="fas fa-image text-white text-6xl opacity-50"></i>
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/40"></div>
        
        {/* Content */}
        <div className="absolute inset-0 flex items-end">
          <div className="w-full px-4 sm:px-6 lg:px-8 pb-12">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4"
                  style={{ fontFamily: 'DM Sans, sans-serif' }}>
                {post.title}
              </h1>
              <div className="flex items-center text-white/90 text-sm space-x-4">
                <span style={{ fontFamily: 'Inter, sans-serif' }}>
                  {post.date}
                </span>
                <span style={{ fontFamily: 'Inter, sans-serif' }}>
                  BY {post.author.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Article Content */}
            <div className="lg:col-span-3">
              <article className="bg-white rounded-lg p-8 shadow-sm border border-gray-100">
                {/* Article Meta */}
                <div className="mb-8 pb-6 border-b border-gray-100">
                  <p className="text-lg leading-relaxed mb-4"
                     style={{ 
                       fontFamily: 'Inter, sans-serif',
                       color: '#666666'
                     }}>
                    {post.excerpt}
                  </p>
                  
                  {/* Categories and Tags */}
                  <div className="space-y-3">
                    {/* Categories */}
                    {post.categories.length > 0 && (
                      <div>
                        <span className="text-sm font-medium mr-2"
                              style={{
                                fontFamily: 'DM Sans, sans-serif',
                                color: '#2b2b2b'
                              }}>
                          Categories:
                        </span>
                        <div className="inline-flex flex-wrap gap-2">
                          {post.categories.map((category, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 text-sm rounded-full"
                              style={{
                                backgroundColor: '#F3F4FA',
                                color: '#575C8D',
                                fontFamily: 'Inter, sans-serif'
                              }}
                            >
                              {category}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Tags */}
                    {post.tags && post.tags.length > 0 && (
                      <div>
                        <span className="text-sm font-medium mr-2"
                              style={{
                                fontFamily: 'DM Sans, sans-serif',
                                color: '#2b2b2b'
                              }}>
                          Tags:
                        </span>
                        <div className="inline-flex flex-wrap gap-2">
                          {post.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 text-sm rounded-full"
                              style={{
                                backgroundColor: '#FFF5F8',
                                color: '#E53274',
                                fontFamily: 'Inter, sans-serif'
                              }}
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Article Content */}
                <div
                  className="prose prose-lg max-w-none mb-8"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    color: '#2b2b2b'
                  }}
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />



                {/* Social Share */}
                <div className="pt-6 border-t border-gray-100">
                  <div className="flex items-center space-x-4">
                    <span className="text-sm font-medium"
                          style={{
                            fontFamily: 'DM Sans, sans-serif',
                            color: '#2b2b2b'
                          }}>
                      Share this article:
                    </span>

                    {/* Social Share Buttons */}
                    <div className="flex space-x-3">
                      <a
                        href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-[#3b5998] flex items-center justify-center text-white hover:opacity-80 transition-opacity"
                      >
                        <i className="fab fa-facebook-f text-sm"></i>
                      </a>
                      <a
                        href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(post.title)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-[#1da1f2] flex items-center justify-center text-white hover:opacity-80 transition-opacity"
                      >
                        <i className="fab fa-twitter text-sm"></i>
                      </a>
                      <a
                        href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-[#0077b5] flex items-center justify-center text-white hover:opacity-80 transition-opacity"
                      >
                        <i className="fab fa-linkedin-in text-sm"></i>
                      </a>
                      <a
                        href={`https://wa.me/?text=${encodeURIComponent(post.title + ' ' + shareUrl)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-[#25d366] flex items-center justify-center text-white hover:opacity-80 transition-opacity"
                      >
                        <i className="fab fa-whatsapp text-sm"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </article>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-8">
              {/* Author Profile */}
              <AuthorProfile authorData={post.authorData} />

              {/* Categories */}
              <Categories />
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <ContactInfoSection />
    </div>
  );
}
