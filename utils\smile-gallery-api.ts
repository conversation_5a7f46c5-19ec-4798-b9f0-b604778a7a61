import { SmileGalleryPost, MediaResponse, SmileGalleryCardData } from '@/types/smile-gallery';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Treatment icon mapping based on treatment names
const getTreatmentIcon = (treatmentName: string): string => {
  const name = treatmentName.toLowerCase();
  
  if (name.includes('invisalign') || name.includes('aligner')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/invisalign-2123563.svg';
  } else if (name.includes('implant')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/implant-2123546.svg';
  } else if (name.includes('whitening') || name.includes('bleaching')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/teeth-whitening-2123558.svg';
  } else if (name.includes('veneer')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/veneers-2123557.svg';
  } else if (name.includes('crown') || name.includes('cap')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/crown-2123549.svg';
  } else if (name.includes('braces') || name.includes('orthodontic')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/orthodontics-2123564.svg';
  } else if (name.includes('root canal') || name.includes('endodontic')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/endodontics-2123547.svg';
  } else if (name.includes('extraction')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/extraction-tooth-2123553.svg';
  } else if (name.includes('filling')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/filling-treatment-2123570.svg';
  } else if (name.includes('cleaning') || name.includes('scaling')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/scaling-treatment-2123562.svg';
  } else {
    // Default dental icon
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/smile-4866975.svg';
  }
};

// Fetch media URL by ID
export async function fetchMediaUrl(mediaId: number): Promise<string> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/media/${mediaId}?_fields=guid`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch media: ${response.status} ${response.statusText}`);
    }
    const media: MediaResponse = await response.json();
    return media.guid.rendered;
  } catch (error) {
    console.error('Error fetching media URL:', error);
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMDAgMTUwTDE3NSAxMjVIMjI1TDIwMCAxNTBaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0yMDAgMTUwTDE3NSAxNzVIMjI1TDIwMCAxNTBaIiBmaWxsPSIjOUNBM0FGIi8+CjwvdGc+Cjwvc3ZnPgo='; // Base64 encoded placeholder SVG
  }
}

// Fetch smile gallery posts
export async function fetchSmileGalleryPosts(): Promise<SmileGalleryPost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/smile-gallery/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch smile gallery posts: ${response.status} ${response.statusText}`);
    }
    const posts: SmileGalleryPost[] = await response.json();
    return posts;
  } catch (error) {
    console.error('Error fetching smile gallery posts:', error);
    // Return empty array to prevent app crashes
    return [];
  }
}

// Helper function to extract text from HTML content
function extractTextFromHtml(html: string): string {
  // Remove HTML tags and decode HTML entities
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#039;/g, "'") // Replace &#039; with '
    .trim();
}

// Process smile gallery posts into card data
export async function processSmileGalleryData(posts: SmileGalleryPost[]): Promise<SmileGalleryCardData[]> {
  const processedData: SmileGalleryCardData[] = [];

  for (const post of posts) {
    try {
      const beforeImageUrl = await fetchMediaUrl(post.acf.before_image);
      const afterImageUrl = await fetchMediaUrl(post.acf.after_image);

      // Extract description from content
      const description = extractTextFromHtml(post.content.rendered) || 'Treatment Duration: 12 months';

      processedData.push({
        id: post.id,
        title: post.title.rendered,
        beforeImageUrl,
        afterImageUrl,
        description,
        treatmentIcon: getTreatmentIcon(post.title.rendered)
      });
    } catch (error) {
      console.error(`Error processing post ${post.id}:`, error);
    }
  }

  return processedData;
}

// Get smile gallery data for homepage (first 4 posts)
export async function getHomepageSmileGallery(): Promise<SmileGalleryCardData[]> {
  const posts = await fetchSmileGalleryPosts();
  const processedData = await processSmileGalleryData(posts);
  return processedData.slice(0, 4);
}

// Get all smile gallery data for gallery page
export async function getAllSmileGallery(): Promise<SmileGalleryCardData[]> {
  const posts = await fetchSmileGalleryPosts();
  return await processSmileGalleryData(posts);
}
