import { ServicePost, ServiceCardData } from '@/types/services';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Dental icon mapping based on service names
const getServiceIcon = (serviceName: string): string => {
  const name = serviceName.toLowerCase();
  
  // Map service names to appropriate dental icons
  if (name.includes('implant')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/dental-implant-2268998.svg';
  } else if (name.includes('root canal') || name.includes('endodontic')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/endodontics-2123547.svg';
  } else if (name.includes('braces') || name.includes('orthodontic')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/braces-2268986.svg';
  } else if (name.includes('whitening') || name.includes('bleaching')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/teeth-whitening-2123558.svg';
  } else if (name.includes('cleaning') || name.includes('scaling')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/scaling-treatment-2123562.svg';
  } else if (name.includes('extraction')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/extraction-tooth-2123553.svg';
  } else if (name.includes('filling') || name.includes('restoration')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/filling-treatment-2123570.svg';
  } else if (name.includes('crown') || name.includes('cap')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/crown-2123549.svg';
  } else if (name.includes('denture') || name.includes('false teeth')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/dentures-2123573.svg';
  } else if (name.includes('veneer')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/veneers-2123557.svg';
  } else if (name.includes('wisdom')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/wisdom-teeth-2123545.svg';
  } else if (name.includes('checkup') || name.includes('examination')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/dental-checkup-2268985.svg';
  } else if (name.includes('gum') || name.includes('periodontal')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/gum-surgery-2123569.svg';
  } else if (name.includes('invisalign')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/invisalign-2123563.svg';
  } else if (name.includes('bridge')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/dental-bridge-2123576.svg';
  } else if (name.includes('pain') || name.includes('ache')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/toothache-2268990.svg';
  } else if (name.includes('pediatric') || name.includes('child')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/anxiety-kid-2123579.svg';
  } else if (name.includes('x-ray') || name.includes('radiograph')) {
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/x-ray-teeth-2123556.svg';
  } else {
    // Default dental icon
    return 'https://wp.dentaloffice.io/solis/wp-content/uploads/2025/04/smile-4866975.svg';
  }
};

// Fetch service posts from WordPress API
export async function fetchServicePosts(): Promise<ServicePost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/service/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch service posts: ${response.status} ${response.statusText}`);
    }
    const posts: ServicePost[] = await response.json();
    return posts;
  } catch (error) {
    console.error('Error fetching service posts:', error);
    // Return empty array to prevent app crashes
    return [];
  }
}

// Fetch single service post by slug
export async function fetchServiceBySlug(slug: string): Promise<ServicePost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/service?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch service: ${response.status} ${response.statusText}`);
    }
    const posts: ServicePost[] = await response.json();
    if (posts.length > 0) {
      const post = posts[0];
      // Process ACF FAQ data into array format
      if (post.acf) {
        const faqArray = [];
        for (let i = 1; i <= 10; i++) {
          const question = post.acf[`question_${i}`];
          const answer = post.acf[`answer_${i}`];
          if (question && answer) {
            faqArray.push({ question, answer });
          }
        }
        if (faqArray.length > 0) {
          post.acf.faq = faqArray;
        }
      }
      return post;
    }
    return null;
  } catch (error) {
    console.error('Error fetching service by slug:', error);
    return null;
  }
}

// Helper function to extract text from HTML content
function extractTextFromHtml(html: string): string {
  // Remove HTML tags and decode HTML entities
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#039;/g, "'") // Replace &#039; with '
    .trim();
}

// Process service posts into card data
export async function processServiceData(posts: ServicePost[]): Promise<ServiceCardData[]> {
  const processedData: ServiceCardData[] = [];

  for (const post of posts) {
    try {
      // Extract excerpt from excerpt field or content
      const excerpt = extractTextFromHtml(post.excerpt.rendered) ||
                     extractTextFromHtml(post.content.rendered).substring(0, 150) + '...';

      // Process ACF FAQ data into array format
      const processedAcf = { ...post.acf };
      if (post.acf) {
        const faqArray = [];
        for (let i = 1; i <= 10; i++) {
          const question = post.acf[`question_${i}`];
          const answer = post.acf[`answer_${i}`];
          if (question && answer) {
            faqArray.push({ question, answer });
          }
        }
        if (faqArray.length > 0) {
          processedAcf.faq = faqArray;
        }
      }

      processedData.push({
        id: post.id,
        title: post.title.rendered,
        excerpt,
        slug: post.slug,
        icon: getServiceIcon(post.title.rendered),
        content: post.content.rendered,
        acf: processedAcf
      });
    } catch (error) {
      console.error(`Error processing service post ${post.id}:`, error);
    }
  }

  return processedData;
}

// Get all services data
export async function getAllServices(): Promise<ServiceCardData[]> {
  const posts = await fetchServicePosts();
  return await processServiceData(posts);
}

// Get services data for homepage (first 6 services)
export async function getHomepageServices(): Promise<ServiceCardData[]> {
  const posts = await fetchServicePosts();
  const processedData = await processServiceData(posts);
  return processedData.slice(0, 6);
}
