'use client';

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { getAllServices } from "@/utils/services-api";
import { getAllLegalPages } from "@/utils/legal-pages-api";
import { ServiceCardData } from "@/types/services";
import { LegalPageData } from "@/types/legal-pages";

export default function Footer() {
  const [services, setServices] = useState<ServiceCardData[]>([]);
  const [legalPages, setLegalPages] = useState<LegalPageData[]>([]);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const fetchServices = async () => {
      try {
        const servicesData = await getAllServices();
        setServices(servicesData);
      } catch (error) {
        console.error('Error fetching services for footer:', error);
      }
    };

    const fetchLegalPages = async () => {
      try {
        const legalPagesData = await getAllLegalPages();
        console.log('Legal pages data received:', legalPagesData);
        setLegalPages(legalPagesData || []);
      } catch (error) {
        console.error('Error fetching legal pages for footer:', error);
        setLegalPages([]); // Set empty array on error
      }
    };

    fetchServices();
    fetchLegalPages();
  }, []);

  // Static fallback services to prevent hydration mismatch
  const fallbackServices = [
    'Root Canal Treatment',
    'Dental Implants',
    'Teeth Whitening',
    'Orthodontics',
    'Dental Cleaning',
    'Tooth Extraction',
    'Dental Crowns',
    'Dental Bridges',
    'Gum Treatment'
  ];



  return (
    <footer>
      {/* Areas We Serve Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-6xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Areas We Serve in Vellore
            </h2>
            <p className="text-lg md:text-xl max-w-3xl mx-auto"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b'
               }}>
              Providing quality dental care to patients throughout Vellore and surrounding areas.
            </p>
          </div>

          {/* Areas Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            {[
              { name: 'Gandhi Nagar', distance: '0.5 km' },
              { name: 'Suthanthira Ponvizha Nagar', distance: '0.2 km' },
              { name: 'Katpadi', distance: '3 km' },
              { name: 'Bagayam', distance: '5 km' },
              { name: 'Thorapadi', distance: '4 km' },
              { name: 'Sathuvachari', distance: '6 km' },
              { name: 'Rangapuram', distance: '6 km' },
              { name: 'Kosapet', distance: '4 km' }
            ].map((area, index) => (
              <div key={index} className="text-center p-4 bg-white rounded-lg border-2 border-[#E53274] hover:shadow-lg transition-all duration-300">
                <div className="mb-2">
                  <svg className="w-6 h-6 mx-auto" style={{ color: '#E53274' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                  </svg>
                </div>
                <h3 className="font-semibold text-base mb-1"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#2b2b2b'
                    }}>
                  {area.name}
                </h3>
                <p className="text-sm"
                   style={{
                     fontFamily: 'Inter, sans-serif',
                     color: '#575C8D'
                   }}>
                  {area.distance}
                </p>
              </div>
            ))}
          </div>

          {/* Description */}
          <div className="text-center mb-8">
            <p className="text-base md:text-lg max-w-4xl mx-auto"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b'
               }}>
              We welcome patients from all areas of Vellore and nearby towns. Our central location makes us easily accessible from anywhere in the region.
            </p>
          </div>

          {/* See Our Clinic Button */}
          <div className="text-center">
            <Link href="/clinic/indira-dental-clinic"
                  className="inline-block px-8 py-3 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105 hover:shadow-lg"
                  style={{
                    backgroundColor: '#E53274',
                    fontFamily: 'DM Sans, sans-serif'
                  }}>
              See Our Clinic
            </Link>
          </div>
        </div>
      </section>

      {/* Main Footer */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#E53274' }}>
        <div className="max-w-6xl mx-auto">
          {/* Logo Section */}
          <div className="text-center mb-16">
            <div className="inline-block bg-white rounded-2xl p-6 mb-8 hover:scale-105 transition-transform duration-300">
              <Image
                src="/Website Logo - Black Text.svg"
                alt="Indira Dental Clinic"
                width={300}
                height={80}
                className="h-16 w-auto"
              />
            </div>

            {/* Separator Line */}
            <div className="w-full h-1 bg-white rounded"></div>
          </div>

          {/* Footer Content Grid */}
          <div className="grid md:grid-cols-3 gap-8 lg:gap-12 xl:gap-16">
            {/* Our Services Column */}
            <div>
              <div className="bg-white rounded-lg px-4 py-2 inline-block mb-6">
                <h3 className="text-lg font-bold"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#E53274'
                    }}>
                  Our Services
                </h3>
              </div>
              <ul className="space-y-3">
                {isClient && services.length > 0 ? (
                  services.map((service, index) => (
                    <li key={index}>
                      <Link href={`/services/${service.slug}`} className="text-white hover:text-gray-200 transition-colors"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           fontSize: '16px'
                         }}>
                        {service.title}
                      </Link>
                    </li>
                  ))
                ) : (
                  // Static fallback services to prevent hydration mismatch
                  fallbackServices.map((service, index) => (
                    <li key={index}>
                      <Link href="/services" className="text-white hover:text-gray-200 transition-colors"
                         style={{
                           fontFamily: 'Inter, sans-serif',
                           fontSize: '16px'
                         }}>
                        {service}
                      </Link>
                    </li>
                  ))
                )}
              </ul>
            </div>

            {/* Vellore Locations Column */}
            <div>
              <div className="bg-white rounded-lg px-4 py-2 inline-block mb-6">
                <h3 className="text-lg font-bold"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#E53274'
                    }}>
                  Vellore Locations
                </h3>
              </div>
              <ul className="space-y-4">
                <li>
                  <div className="mb-2">
                    <h4 className="font-semibold text-white mb-1"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#FDFDFD'
                        }}>
                      Our Dental Clinic In Vellore Locations
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (flagship center)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Full-service dental care & tourism hub
                    </p>
                  </div>
                </li>
                <li>
                  <div className="mb-2">
                    <h4 className="font-semibold text-white mb-1"
                        style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Katpadi
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (near railway station)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Specializing in dental implants & RCT
                    </p>
                  </div>
                </li>
                <li>
                  <div className="mb-2">
                    <h4 className="font-semibold text-white mb-1"
                        style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Gandhi Nagar
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (central location)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Cosmetic dentistry & family care
                    </p>
                  </div>
                </li>
                <li>
                  <div className="mb-2">
                    <h4 className="font-semibold text-white mb-1"
                        style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Sathuvachari
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (residential area)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Pediatric dentistry & orthodontics
                    </p>
                  </div>
                </li>
                <li>
                  <div className="mb-2">
                    <h4 className="font-semibold text-white mb-1"
                        style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Bagayam
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (near CMC)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Advanced surgical procedures
                    </p>
                  </div>
                </li>
                <li>
                  <div className="mb-4">
                    <h4 className="font-semibold text-white mb-1"
                        style={{ fontFamily: 'DM Sans, sans-serif' }}>
                      Ranipet
                    </h4>
                    <p className="text-sm text-white mb-1"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      (industrial zone)
                    </p>
                    <p className="text-sm text-white"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      Emergency dental care & general dentistry
                    </p>
                  </div>
                </li>
                <li>
                  <Link href="/clinic/indira-dental-clinic"
                        className="inline-flex items-center gap-2 text-white hover:text-gray-200 transition-colors font-semibold"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#FDFDFD'
                        }}>
                    View All Locations
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                    </svg>
                  </Link>
                </li>
              </ul>
            </div>

            {/* Quick Links & Contact Column */}
            <div>
              <div className="bg-white rounded-lg px-4 py-2 inline-block mb-6">
                <h3 className="text-lg font-bold"
                    style={{
                      fontFamily: 'DM Sans, sans-serif',
                      color: '#E53274'
                    }}>
                  Quick Links
                </h3>
              </div>
              <ul className="space-y-3 mb-8">
                {/* Static Quick Links */}
                {[
                  { text: 'Home', href: '/' },
                  { text: 'About Us', href: '/about' },
                  { text: 'Services', href: '/services' },
                  { text: 'Blog', href: '/blog' },
                  { text: 'Dentists', href: '/dentists' },
                  { text: 'Smile Gallery', href: '/smile-gallery' },
                  { text: 'Dental Tourism', href: '/dental-tourism' },
                  { text: 'Contact Us', href: '/contact' },
                  { text: 'FAQ', href: '/faq' },
                  { text: 'Book Appointment', href: '/book-appointment' }
                ].map((link, index) => (
                  <li key={`static-${index}`}>
                    <Link href={link.href} className="text-white hover:text-gray-200 transition-colors"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         fontSize: '16px'
                       }}>
                      {link.text}
                    </Link>
                  </li>
                ))}

                {/* Dynamic Legal Pages */}
                {isClient && legalPages.length > 0 && legalPages.map((legalPage) => (
                  <li key={`legal-${legalPage.id}`}>
                    <Link href={`/legal/${legalPage.slug}`} className="text-white hover:text-gray-200 transition-colors"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         fontSize: '16px'
                       }}>
                      {legalPage.title}
                    </Link>
                  </li>
                ))}
              </ul>

              {/* Contact Info Box */}
              <div className="rounded-lg p-6 border-2 border-white">
                <div className="space-y-4">
                  {/* Phone */}
                  <div className="flex items-center gap-3">
                    <svg className="w-5 h-5 text-white flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                    <a href="tel:+917010650063"
                       className="text-white font-semibold hover:text-gray-200 transition-colors"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      +91-7010650063
                    </a>
                  </div>

                  {/* Email */}
                  <div className="flex items-center gap-3">
                    <svg className="w-5 h-5 text-white flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                    </svg>
                    <a href="mailto:<EMAIL>"
                       className="text-white font-semibold hover:text-gray-200 transition-colors"
                       style={{ fontFamily: 'Inter, sans-serif' }}>
                      <EMAIL>
                    </a>
                  </div>

                  {/* Address */}
                  <div className="flex items-start gap-3">
                    <svg className="w-5 h-5 text-white flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                    </svg>
                    <span className="text-white text-sm leading-relaxed"
                          style={{ fontFamily: 'Inter, sans-serif' }}>
                      3rd Floor, 54, Katpadi Main Rd, Suthanthira Ponvizha Nagar, Gandhi Nagar, Vellore, Tamil Nadu 632006
                    </span>
                  </div>
                </div>
              </div>

              {/* Social Media */}
              <div className="mt-8">
                <p className="text-white font-semibold mb-4"
                   style={{ fontFamily: 'DM Sans, sans-serif' }}>
                  Follow Us on:
                </p>
                <div className="flex gap-6">
                  <Link href="https://www.facebook.com/indiradentalclinicvellore/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white hover:text-gray-200 transition-colors">
                    <i className="fa-brands fa-facebook text-2xl"></i>
                  </Link>
                  <Link href="https://www.instagram.com/indiradentalvellore/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white hover:text-gray-200 transition-colors">
                    <i className="fa-brands fa-instagram text-2xl"></i>
                  </Link>
                  <Link href="https://www.linkedin.com/company/indira-dental-clinic-vellore"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white hover:text-gray-200 transition-colors">
                    <i className="fa-brands fa-linkedin text-2xl"></i>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Copyright Footer */}
      <section className="py-6 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#575C8D' }}>
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center text-center md:text-left">
            <p className="text-white mb-4 md:mb-0"
               style={{ fontFamily: 'Inter, sans-serif' }}>
              © 2025 Indira Dental Clinic. All Rights Reserved.
            </p>
            <p className="text-white"
               style={{ fontFamily: 'Inter, sans-serif' }}>
              Designed and Managed by <a href="https://www.clinicecho.com">ClinicEcho</a>
            </p>
          </div>
        </div>
      </section>
    </footer>
  );
}
