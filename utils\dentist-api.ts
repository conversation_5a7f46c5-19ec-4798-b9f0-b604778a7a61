import { DentistPost, DentistCardData, SpecialityData } from '@/types/dentist';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Fetch all dentists from WordPress API
export async function fetchDentists(): Promise<DentistPost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/dentist/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch dentists: ${response.status} ${response.statusText}`);
    }

    const dentists: DentistPost[] = await response.json();
    return dentists;
  } catch (error) {
    console.error('Error fetching dentists:', error);
    return [];
  }
}

// Fetch dentist images from WordPress media API
export async function fetchDentistImages(imageIds: number[]): Promise<string[]> {
  const images: string[] = [];
  for (const imageId of imageIds) {
    if (imageId) {
      try {
        const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/media/${imageId}`);
        if (response.ok) {
          const mediaData = await response.json();
          images.push(mediaData.source_url);
        }
      } catch (error) {
        console.error('Error fetching dentist image:', error);
      }
    }
  }
  return images;
}

// Fetch speciality names from WordPress API
export async function fetchSpecialityNames(specialityIds: number[]): Promise<string[]> {
  const specialityNames: string[] = [];

  for (const specialityId of specialityIds) {
    if (specialityId) {
      try {
        const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/speciality/${specialityId}`);
        if (response.ok) {
          const specialityData: SpecialityData = await response.json();
          specialityNames.push(specialityData.name);
        }
      } catch (error) {
        console.error('Error fetching speciality:', error);
        // Fallback to generic names if API fails
        specialityNames.push(`Speciality ${specialityId}`);
      }
    }
  }

  return specialityNames;
}

// Process dentist data for display
export async function processDentistData(dentist: DentistPost): Promise<DentistCardData> {
  const cardData: DentistCardData = {
    id: dentist.id,
    title: dentist.title.rendered,
    content: dentist.content.rendered,
    excerpt: dentist.excerpt.rendered,
    slug: dentist.slug,
    featured_media: dentist.featured_media,
    specialities: dentist.speciality || [],
    clinic_location: dentist.acf?.clinic_location,

    // Professional Information from ACF
    qualification: dentist.acf?.qualification,
    experience_years: dentist.acf?.experience_years,
    designation: dentist.acf?.designation,
    registration_number: dentist.acf?.registration_number,
    education: dentist.acf?.education,
    certifications: dentist.acf?.certifications,

    // Contact Information
    phone_number: dentist.acf?.phone_number,
    email: dentist.acf?.email,
    consultation_hours: dentist.acf?.consultation_hours,

    // Professional Details
    languages_spoken: dentist.acf?.languages_spoken,
    areas_of_expertise: dentist.acf?.areas_of_expertise,
    awards_achievements: dentist.acf?.awards_achievements,
    professional_memberships: dentist.acf?.professional_memberships,

    // Additional Information
    bio: dentist.acf?.bio,
    motto: dentist.acf?.motto,
    consultation_fee: dentist.acf?.consultation_fee,
    availability: dentist.acf?.availability,

    // Social Media & Online Presence
    website: dentist.acf?.website,
    linkedin: dentist.acf?.linkedin,
    facebook: dentist.acf?.facebook,
    instagram: dentist.acf?.instagram,

    // Practice Information
    years_of_practice: dentist.acf?.years_of_practice,
    patients_treated: dentist.acf?.patients_treated,
    success_rate: dentist.acf?.success_rate,

    // Services & Treatments
    treatments_offered: dentist.acf?.treatments_offered,
    special_procedures: dentist.acf?.special_procedures,
    equipment_used: dentist.acf?.equipment_used,
  };

  // Fetch speciality names
  if (dentist.speciality && dentist.speciality.length > 0) {
    try {
      const specialityNames = await fetchSpecialityNames(dentist.speciality);
      cardData.speciality_names = specialityNames;
    } catch (error) {
      console.error('Error fetching speciality names:', error);
    }
  }

  // Fetch featured image if available
  if (dentist.featured_media) {
    try {
      const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/media/${dentist.featured_media}`);
      if (response.ok) {
        const mediaData = await response.json();
        cardData.image_url = mediaData.source_url;
      }
    } catch (error) {
      console.error('Error fetching featured image:', error);
    }
  }

  // Fetch dentist images if available
  if (dentist.acf) {
    const imageIds = [
      dentist.acf.dentist_image_1,
      dentist.acf.dentist_image_2,
      dentist.acf.dentist_image_3,
      dentist.acf.dentist_image_4,
      dentist.acf.dentist_image_5
    ].filter(id => id && typeof id === 'number') as number[];

    if (imageIds.length > 0) {
      const images = await fetchDentistImages(imageIds);
      cardData.dentist_images = images;
    }
  }

  return cardData;
}

// Get all dentists with processed data
export async function getAllDentists(): Promise<DentistCardData[]> {
  const dentists = await fetchDentists();
  const processedDentists: DentistCardData[] = [];

  for (const dentist of dentists) {
    const processedDentist = await processDentistData(dentist);
    processedDentists.push(processedDentist);
  }

  return processedDentists;
}

// Fetch single dentist by slug
export async function fetchDentistBySlug(slug: string): Promise<DentistPost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/dentist?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch dentist: ${response.status} ${response.statusText}`);
    }
    
    const dentists: DentistPost[] = await response.json();
    if (dentists.length > 0) {
      return dentists[0];
    }
    return null;
  } catch (error) {
    console.error('Error fetching dentist by slug:', error);
    return null;
  }
}
