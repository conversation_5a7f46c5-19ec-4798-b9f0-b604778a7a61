import { ClinicPost } from '@/types/clinic';

const API_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://app.dentalclinicinvellore.in';

// Fetch clinic posts from WordPress API
export async function fetchClinicPosts(): Promise<ClinicPost[]> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/clinic/`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Failed to fetch clinic posts: ${response.status} ${response.statusText}`);
    }

    const posts: ClinicPost[] = await response.json();
    return posts;
  } catch (error) {
    console.error('Error fetching clinic posts:', error);
    return [];
  }
}

// Fetch single clinic post by slug
export async function fetchClinicBySlug(slug: string): Promise<ClinicPost | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${API_BASE_URL}/wp-json/wp/v2/clinic?slug=${slug}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Prevent caching issues
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      // If clinic endpoint doesn't exist, return mock data for testing
      if (response.status === 404 && (slug === 'indira-dental-clinic-vellore' || slug === 'indira-dental-clinic')) {
        return getMockClinicData();
      }
      throw new Error(`Failed to fetch clinic: ${response.status} ${response.statusText}`);
    }
    
    const posts: ClinicPost[] = await response.json();
    if (posts.length > 0) {
      const post = posts[0];
      // Process ACF data if available
      if (post.acf) {
        // Process business hours
        const businessHours = [];
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        for (const day of days) {
          const openTime = (post.acf as any)[`${day}_open`];
          const closeTime = (post.acf as any)[`${day}_close`];
          const isClosed = (post.acf as any)[`${day}_closed`];
          
          if (openTime && closeTime && !isClosed) {
            businessHours.push({
              day: day.charAt(0).toUpperCase() + day.slice(1),
              open: openTime,
              close: closeTime,
              closed: false
            });
          } else {
            businessHours.push({
              day: day.charAt(0).toUpperCase() + day.slice(1),
              open: '',
              close: '',
              closed: true
            });
          }
        }
        post.acf.businessHours = businessHours;

        // Process reviews
        const reviews = [];
        for (let i = 1; i <= 10; i++) {
          const reviewerName = (post.acf as any)[`review_${i}_name`];
          const reviewText = (post.acf as any)[`review_${i}_text`];
          const reviewRating = (post.acf as any)[`review_${i}_rating`];
          const reviewDate = (post.acf as any)[`review_${i}_date`];
          
          if (reviewerName && reviewText && reviewRating) {
            reviews.push({
              name: reviewerName,
              text: reviewText,
              rating: parseInt(reviewRating),
              date: reviewDate || new Date().toISOString()
            });
          }
        }
        if (reviews.length > 0) {
          post.acf.reviews = reviews;
        }

        // Process facilities
        const facilities = [];
        for (let i = 1; i <= 20; i++) {
          const facility = (post.acf as any)[`facility_${i}`];
          if (facility) {
            facilities.push(facility);
          }
        }
        if (facilities.length > 0) {
          post.acf.facilities = facilities;
        }

        // Process services offered
        const servicesOffered = [];
        for (let i = 1; i <= 20; i++) {
          const service = (post.acf as any)[`service_${i}`];
          if (service) {
            servicesOffered.push(service);
          }
        }
        if (servicesOffered.length > 0) {
          post.acf.servicesOffered = servicesOffered;
        }
      }
      return post;
    }
    return null;
  } catch (error) {
    console.error('Error fetching clinic by slug:', error);
    // Return mock data for testing if it's the expected slug
    if (slug === 'indira-dental-clinic-vellore' || slug === 'indira-dental-clinic') {
      return getMockClinicData();
    }
    return null;
  }
}

// Get all clinics data
export async function getAllClinics(): Promise<ClinicPost[]> {
  return await fetchClinicPosts();
}

// Mock data for testing when clinic endpoint doesn't exist
function getMockClinicData(): ClinicPost {
  return {
    id: 86,
    date: "2025-06-15T08:30:09",
    date_gmt: "2025-06-15T08:30:09",
    guid: {
      rendered: "https://app.dentalclinicinvellore.in/?post_type=clinic&#038;p=86"
    },
    modified: "2025-06-15T08:45:54",
    modified_gmt: "2025-06-15T08:45:54",
    slug: "indira-dental-clinic",
    status: "publish",
    type: "clinic",
    link: "https://app.dentalclinicinvellore.in/clinic/indira-dental-clinic/",
    title: {
      rendered: "Indira Dental Clinic"
    },
    content: {
      rendered: "\n<p>Indira Dental Clinic has been serving the Vellore community for years, providing comprehensive dental care with a focus on patient comfort and advanced treatment techniques. Our clinic is equipped with state-of-the-art technology and staffed by experienced dental professionals who are committed to delivering the highest quality care.</p>\n<p>We offer a wide range of dental services including preventive care, restorative treatments, cosmetic dentistry, and emergency dental services. Our goal is to help you achieve and maintain optimal oral health in a comfortable and welcoming environment.</p>\n",
      protected: false
    },
    excerpt: {
      rendered: "<p>Your trusted dental care partner in Katpadi, Vellore, providing exceptional dental services with a commitment to patient comfort and satisfaction.</p>\n",
      protected: false
    },
    author: 1,
    featured_media: 0,
    comment_status: "closed",
    ping_status: "closed",
    sticky: false,
    template: "",
    format: "standard",
    meta: [],
    categories: [],
    tags: [],
    acf: {
      clinic_location: {
        address: "Indira Dental Clinic | Dr Rockson Samuel | Top Dentist in Vellore for RCT, Braces, Implants, & Dental Fillings, Katpadi Main Road, Suthanthira Ponvizha Nagar, Gandhi Nagar, Vellore, Tamil Nadu, India",
        lat: 12.9540278,
        lng: 79.1369615,
        zoom: 14,
        place_id: "ChIJD2JDmE45rTsRhuCRCfuypMc",
        name: "Indira Dental Clinic | Dr Rockson Samuel | Top Dentist in Vellore for RCT, Braces, Implants, & Dental Fillings",
        street_number: 54,
        street_name: "Katpadi Main Road",
        street_name_short: "Katpadi Main Rd",
        city: "Vellore",
        state: "Tamil Nadu",
        state_short: "TN",
        post_code: 632006,
        country: "India",
        country_short: "IN"
      },
      clinic_image_1: 70,
      clinic_image_2: 71,
      clinic_image_3: "",
      clinic_image_4: "",
      clinic_image_5: ""
    },
    _links: {
      self: [
        {
          href: "https://app.dentalclinicinvellore.in/wp-json/wp/v2/clinic/86",
          targetHints: {
            allow: [
              "GET"
            ]
          }
        }
      ],
      collection: [
        {
          href: "https://app.dentalclinicinvellore.in/wp-json/wp/v2/clinic"
        }
      ],
      about: [
        {
          href: "https://app.dentalclinicinvellore.in/wp-json/wp/v2/types/clinic"
        }
      ],
      "wp:attachment": [
        {
          href: "https://app.dentalclinicinvellore.in/wp-json/wp/v2/media?parent=86"
        }
      ],
      curies: [
        {
          name: "wp",
          href: "https://api.w.org/{rel}",
          templated: true
        }
      ]
    }
  };
}
