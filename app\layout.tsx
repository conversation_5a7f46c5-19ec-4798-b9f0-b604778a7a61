import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import TopHeader from "@/components/ui/header/top";
import MainHeader from "@/components/ui/header/main";
import Footer from "@/components/ui/footer";
import ErrorBoundary from "@/components/ui/ErrorBoundary";
import AccessibilityWidget from "@/components/ui/AccessibilityWidget";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Indira Dental Clinic - Best Dental Care in Vellore",
  description: "Professional dental care services in Vellore. Expert dentists providing comprehensive dental treatments including cleanings, fillings, crowns, and more.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased max-w-full`}
      >
        <div className="min-h-screen">
          <ErrorBoundary>
            <TopHeader />
            <MainHeader />
            <main id="main-content" className="flex-grow">{children}</main>
            <Footer />
            <AccessibilityWidget />
          </ErrorBoundary>
        </div>
      </body>
    </html>
  );
}
