'use client';

import { useState } from 'react';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';
import Link from 'next/link';

export default function FAQPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('general');

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // FAQ Categories and Data
  const faqCategories = [
    { id: 'general', name: 'General Questions', icon: '❓' },
    { id: 'oral-hygiene', name: 'Oral Hygiene', icon: '🦷' },
    { id: 'treatments', name: 'Treatments & Procedures', icon: '🔧' },
    { id: 'root-canal', name: 'Root Canal Treatment', icon: '🩺' },
    { id: 'dental-implants', name: 'Dental Implants', icon: '⚕️' },
    { id: 'orthodontics', name: 'Orthodontics', icon: '🦷' },
    { id: 'cosmetic', name: 'Cosmetic Dentistry', icon: '✨' },
    { id: 'pediatric', name: 'Pediatric Dentistry', icon: '👶' },
    { id: 'insurance', name: 'Insurance & Payments', icon: '💳' },
    { id: 'appointments', name: 'Appointments', icon: '📅' },
    { id: 'emergency', name: 'Emergency Care', icon: '🚨' }
  ];

  const faqData: Record<string, Array<{question: string, answer: string}>> = {
    general: [
      {
        question: "How often should I visit the dentist?",
        answer: "For most patients, we recommend a dental check-up and professional cleaning every six months. However, Dr. Rockson Samuel may suggest more frequent visits based on your specific oral health needs, especially if you have gum disease, are at high risk for cavities, or have other ongoing dental issues."
      },
      {
        question: "What causes tooth decay?",
        answer: "Tooth decay occurs when plaque, a sticky film of bacteria, builds up on teeth and produces acids that erode tooth enamel. This is typically caused by frequent consumption of sugary foods and beverages, poor oral hygiene, dry mouth conditions, or lack of fluoride. Regular brushing, flossing, and dental check-ups at Indira Dental Clinic can help prevent tooth decay."
      },
      {
        question: "At what age should children first visit the dentist?",
        answer: "Children should have their first dental visit by age 1 or within 6 months after their first tooth erupts. Early dental visits help establish a positive relationship with dental care and allow Dr. Rockson Samuel to monitor oral development and provide guidance on proper oral hygiene practices for children."
      },
      {
        question: "Why do my gums bleed when I brush?",
        answer: "Bleeding gums are often a sign of gingivitis, the early stage of gum disease caused by plaque buildup along the gumline. Improper brushing techniques, using a hard-bristled toothbrush, hormonal changes, certain medications, and vitamin deficiencies can also cause gum bleeding. If you experience persistent bleeding, schedule an appointment with Dr. Rockson Samuel at Indira Dental Clinic for an evaluation."
      },
      {
        question: "What causes bad breath and how can I prevent it?",
        answer: "Bad breath (halitosis) can be caused by poor oral hygiene, gum disease, dry mouth, tobacco use, certain foods, and medical conditions. To prevent bad breath, brush twice daily, floss daily, clean your tongue, stay hydrated, limit odor-causing foods, avoid tobacco, and visit Dr. Rockson Samuel regularly for check-ups and professional cleanings at Indira Dental Clinic."
      }
    ],
    'oral-hygiene': [
      {
        question: "How can I improve my oral hygiene at home?",
        answer: "Maintain good oral hygiene by brushing your teeth twice daily with fluoride toothpaste, flossing daily, using an antimicrobial mouthwash, eating a balanced diet, limiting sugary snacks and beverages, and avoiding tobacco products. Dr. Rockson Samuel can provide personalized recommendations during your visit to Indira Dental Clinic in Vellore."
      },
      {
        question: "What is the proper way to brush teeth?",
        answer: "Use a soft-bristled toothbrush and fluoride toothpaste. Hold the brush at a 45-degree angle to the gums and use gentle, circular motions. Brush all surfaces of each tooth (outer, inner, and chewing surfaces) for a total of two minutes. Don't forget to brush your tongue to remove bacteria. Replace your toothbrush every 3-4 months or sooner if bristles are frayed."
      },
      {
        question: "How important is flossing really?",
        answer: "Flossing is essential for complete oral hygiene as it removes plaque and food particles from between teeth and under the gumline where toothbrushes can't reach. These areas are highly susceptible to decay and gum disease. Dr. Rockson Samuel recommends flossing at least once daily to maintain optimal oral health and prevent cavities and periodontal disease."
      },
      {
        question: "Should I use mouthwash? Which kind is best?",
        answer: "Mouthwash can be a beneficial addition to your oral hygiene routine, but it's not a substitute for brushing and flossing. Therapeutic mouthwashes containing fluoride, antimicrobial agents, or ingredients that reduce plaque can help prevent cavities, reduce gingivitis, and freshen breath. Dr. Rockson Samuel can recommend a specific mouthwash based on your individual oral health needs during your visit to Indira Dental Clinic."
      },
      {
        question: "What's the difference between manual and electric toothbrushes?",
        answer: "Both manual and electric toothbrushes can effectively clean teeth when used properly. Electric toothbrushes may be more efficient at removing plaque and are beneficial for people with limited manual dexterity, orthodontic appliances, or those who tend to brush too hard. The best toothbrush is one that you'll use consistently and correctly. Dr. Rockson Samuel can provide personalized recommendations based on your specific needs."
      }
    ],
    treatments: [
      {
        question: "What options do I have for replacing missing teeth?",
        answer: "At Indira Dental Clinic, we offer several options for replacing missing teeth, including dental implants, bridges, and dentures. Dental implants provide the most natural-looking and functioning replacement, while bridges and dentures may be more suitable depending on your specific situation and budget. Dr. Rockson Samuel will discuss all available options during your consultation."
      },
      {
        question: "What are dental fillings made of?",
        answer: "At Indira Dental Clinic, we offer various filling materials including composite resin (tooth-colored), amalgam (silver), gold, and porcelain. Composite fillings are popular for their aesthetic appeal and are bonded directly to the tooth. Dr. Rockson Samuel will recommend the most appropriate filling material based on the location of the cavity, extent of decay, and your preferences."
      },
      {
        question: "How long do dental crowns last?",
        answer: "With proper care and maintenance, dental crowns typically last 10-15 years, though some can last much longer. Factors affecting longevity include oral hygiene practices, bite habits, material of the crown, and location in the mouth. Regular check-ups with Dr. Rockson Samuel at Indira Dental Clinic will help ensure the integrity and longevity of your dental crowns."
      },
      {
        question: "What is the difference between a bridge and a partial denture?",
        answer: "A dental bridge is a fixed prosthetic that replaces missing teeth by anchoring to adjacent natural teeth or implants. It cannot be removed by the patient. A partial denture is a removable appliance that replaces multiple missing teeth and can be taken out for cleaning. Dr. Rockson Samuel will help determine which option is best for your specific needs at Indira Dental Clinic."
      },
      {
        question: "Are dental X-rays safe?",
        answer: "Modern dental X-rays use very low radiation doses and are considered safe. At Indira Dental Clinic, we use digital X-rays, which reduce radiation exposure by up to 90% compared to traditional film X-rays. We also use protective lead aprons and thyroid collars as additional safety measures. The diagnostic benefits of dental X-rays far outweigh the minimal risks when used appropriately."
      }
    ],
    'root-canal': [
      {
        question: "Is root canal treatment painful?",
        answer: "Modern root canal procedures at Indira Dental Clinic are virtually painless thanks to advanced techniques and effective anesthesia. Most patients report that the procedure is no more uncomfortable than getting a filling. Dr. Rockson Samuel ensures patient comfort throughout the treatment, and any post-procedure discomfort can typically be managed with over-the-counter pain relievers."
      },
      {
        question: "How do I know if I need a root canal?",
        answer: "Signs that you might need a root canal include severe toothache, prolonged sensitivity to hot or cold, tooth discoloration, swelling and tenderness in nearby gums, and a persistent pimple on the gums. However, sometimes there are no symptoms. Regular check-ups with Dr. Rockson Samuel at Indira Dental Clinic can help detect problems before they become severe."
      },
      {
        question: "What happens during a root canal procedure?",
        answer: "During a root canal, Dr. Rockson Samuel will remove the infected or inflamed pulp from inside the tooth, carefully clean and shape the root canal system, then fill and seal the space. Afterward, you'll need a crown or other restoration to protect the tooth and restore it to full function. The procedure is typically completed in one or two appointments at Indira Dental Clinic."
      },
      {
        question: "How long does a root canal take?",
        answer: "A typical root canal treatment at Indira Dental Clinic takes about 60-90 minutes per session. Simple cases may be completed in a single visit, while more complex cases might require two or more appointments. Dr. Rockson Samuel will provide you with a time estimate based on your specific situation during your consultation."
      },
      {
        question: "What's the success rate of root canal treatment?",
        answer: "Root canal treatments have a high success rate of approximately 95%. With proper care, most teeth that have undergone root canal therapy can last a lifetime. Regular check-ups with Dr. Rockson Samuel at Indira Dental Clinic and maintaining good oral hygiene are essential for the long-term success of root canal-treated teeth."
      }
    ],
    'dental-implants': [
      {
        question: "How long do dental implants last?",
        answer: "With proper care and maintenance, dental implants can last a lifetime. The implant itself (the titanium post) has a success rate of over 95% after 10 years. The crown attached to the implant may need replacement after 10-15 years due to normal wear and tear. Regular check-ups with Dr. Rockson Samuel at Indira Dental Clinic will help ensure the longevity of your dental implants."
      },
      {
        question: "Am I a candidate for dental implants?",
        answer: "Most people with good general and oral health are candidates for dental implants. Adequate bone density in the jaw is necessary to support the implant. Certain conditions like uncontrolled diabetes, severe gum disease, or heavy smoking may affect candidacy. Dr. Rockson Samuel will evaluate your specific situation during a comprehensive examination at Indira Dental Clinic."
      },
      {
        question: "What is the process for getting dental implants?",
        answer: "The dental implant process typically involves several steps: initial consultation and planning, implant placement surgery, healing period (osseointegration), placement of the abutment, and finally, attachment of the crown or prosthetic tooth. The entire process usually takes 3-6 months, depending on individual healing times and case complexity. Dr. Rockson Samuel will guide you through each step at Indira Dental Clinic."
      },
      {
        question: "Are dental implants painful?",
        answer: "The implant placement procedure is performed under local anesthesia, so you shouldn't feel pain during the surgery. Some discomfort, swelling, and bruising may occur after the anesthesia wears off, but these symptoms are typically manageable with over-the-counter pain medications. Most patients report that the procedure is less uncomfortable than they anticipated. Dr. Rockson Samuel and the team at Indira Dental Clinic prioritize your comfort throughout the process."
      },
      {
        question: "How do I care for dental implants?",
        answer: "Dental implants require the same care as natural teeth: brushing twice daily, flossing daily, and regular professional cleanings and check-ups with Dr. Rockson Samuel at Indira Dental Clinic. Avoid chewing on hard items like ice or hard candy, and if you grind your teeth, consider getting a night guard to protect your implants and natural teeth from excessive force."
      }
    ],
    orthodontics: [
      {
        question: "What types of braces do you offer?",
        answer: "At Indira Dental Clinic, Dr. Rockson Samuel offers various orthodontic options including traditional metal braces, ceramic (clear) braces, and clear aligners. Each type has its advantages, and the best option depends on your specific needs, lifestyle, and preferences. During your consultation, we'll discuss all available options to find the most suitable treatment for you."
      },
      {
        question: "How long does orthodontic treatment take?",
        answer: "The duration of orthodontic treatment varies depending on the complexity of the case, the type of treatment, and patient compliance. On average, treatment with braces or clear aligners takes 18-24 months. Some minor corrections may take as little as 6 months, while more complex cases might require up to 3 years. Dr. Rockson Samuel will provide you with a personalized treatment timeline during your consultation at Indira Dental Clinic."
      },
      {
        question: "Are clear aligners as effective as traditional braces?",
        answer: "Clear aligners can be as effective as traditional braces for many orthodontic issues, particularly mild to moderate cases. However, complex cases involving significant bite problems or tooth rotations may be better treated with traditional braces. Dr. Rockson Samuel will evaluate your specific needs and recommend the most effective treatment option at Indira Dental Clinic."
      },
      {
        question: "At what age should orthodontic treatment begin?",
        answer: "The American Association of Orthodontists recommends an initial orthodontic evaluation by age 7, as some orthodontic problems are easier to correct when detected early. However, orthodontic treatment can be successful at any age. Dr. Rockson Samuel treats patients of all ages at Indira Dental Clinic, from children to adults seeking to improve their smile and oral health."
      },
      {
        question: "Will I need to wear a retainer after braces?",
        answer: "Yes, wearing a retainer after orthodontic treatment is essential to maintain the new position of your teeth. Initially, you may need to wear your retainer full-time, gradually transitioning to nighttime wear only. Some patients may need to wear retainers indefinitely to prevent teeth from shifting back to their original positions. Dr. Rockson Samuel will provide specific instructions based on your individual case at Indira Dental Clinic."
      }
    ],
    cosmetic: [
      {
        question: "What cosmetic dental procedures do you offer?",
        answer: "At Indira Dental Clinic, Dr. Rockson Samuel offers a comprehensive range of cosmetic dental procedures including teeth whitening, dental veneers, dental bonding, tooth-colored fillings, dental crowns, and smile makeovers. During your consultation, we'll discuss your aesthetic goals and recommend the most appropriate treatments to achieve your desired smile."
      },
      {
        question: "How long do dental veneers last?",
        answer: "With proper care and maintenance, porcelain veneers typically last 10-15 years, while composite veneers usually last 5-7 years. Factors affecting longevity include oral hygiene practices, bite habits, and avoiding behaviors that could damage the veneers (like using teeth as tools or biting very hard foods). Regular check-ups with Dr. Rockson Samuel at Indira Dental Clinic will help ensure the longevity of your veneers."
      },
      {
        question: "Is teeth whitening safe?",
        answer: "Professional teeth whitening performed by Dr. Rockson Samuel at Indira Dental Clinic is safe and effective. We use high-quality whitening agents and customize the treatment to minimize sensitivity and maximize results. Over-the-counter whitening products can be safe when used as directed, but professional treatments provide more predictable results and are performed under dental supervision."
      },
      {
        question: "How long does teeth whitening last?",
        answer: "The results of professional teeth whitening can last from several months to a few years, depending on your habits and lifestyle. Consuming staining substances like coffee, tea, red wine, and tobacco can cause teeth to re-stain more quickly. Touch-up treatments and good oral hygiene can help maintain your bright smile. Dr. Rockson Samuel can provide personalized advice on maintaining your whitening results at Indira Dental Clinic."
      },
      {
        question: "What is the difference between veneers and crowns?",
        answer: "Veneers are thin shells that cover only the front surface of teeth to improve appearance, requiring minimal tooth reduction. Crowns cover the entire tooth and are used when there is significant damage or decay, requiring more substantial tooth preparation. Dr. Rockson Samuel will recommend the most appropriate option based on your specific needs and goals during your consultation at Indira Dental Clinic."
      }
    ],
    pediatric: [
      {
        question: "How can I prepare my child for their first dental visit?",
        answer: "To prepare your child for their first dental visit, talk positively about the dentist, read children's books about dental visits, play pretend dentist at home, and avoid using scary words or sharing negative dental experiences. Let them know that Dr. Rockson Samuel and the team at Indira Dental Clinic will count and clean their teeth to keep them healthy and strong. Maintaining a positive, relaxed attitude helps children feel more comfortable."
      },
      {
        question: "Are dental sealants necessary for children?",
        answer: "Dental sealants are highly recommended for children as they provide an effective barrier against cavity-causing bacteria in the deep grooves of molars, where toothbrushes often can't reach. They're quick, painless to apply, and can reduce cavity risk by up to 80%. Dr. Rockson Samuel typically recommends sealants for permanent molars as soon as they erupt, usually between ages 6-12, at Indira Dental Clinic."
      },
      {
        question: "How important are baby teeth if they're going to fall out anyway?",
        answer: "Baby teeth are crucial despite being temporary. They help children speak clearly, chew properly, and maintain space for permanent teeth. Premature loss of baby teeth can lead to alignment issues with permanent teeth. Additionally, untreated decay in baby teeth can affect the developing permanent teeth beneath them. Dr. Rockson Samuel emphasizes the importance of caring for baby teeth as part of overall oral health at Indira Dental Clinic."
      },
      {
        question: "When should my child start using toothpaste with fluoride?",
        answer: "The American Dental Association recommends using a smear (size of a grain of rice) of fluoride toothpaste for children under 3 years, and a pea-sized amount for children 3-6 years. Fluoride helps strengthen tooth enamel and prevent cavities. Dr. Rockson Samuel can provide personalized guidance on fluoride use based on your child's specific needs and risk factors during your visit to Indira Dental Clinic."
      },
      {
        question: "How can I prevent baby bottle tooth decay?",
        answer: "To prevent baby bottle tooth decay, avoid putting your baby to bed with a bottle containing anything other than water. Clean your baby's gums after feedings with a soft cloth, begin brushing as soon as the first tooth appears, limit sugary drinks, and schedule your child's first dental visit by their first birthday. Dr. Rockson Samuel at Indira Dental Clinic can provide additional guidance on maintaining your child's oral health."
      }
    ],
    insurance: [
      {
        question: "What insurance plans do you accept?",
        answer: "Indira Dental Clinic accepts most major dental insurance plans. Our administrative team will help verify your coverage and benefits before treatment. We also work with patients to maximize their insurance benefits and provide estimates of out-of-pocket costs. Please contact our office at ********** for specific information about your insurance plan."
      },
      {
        question: "Do you offer payment plans?",
        answer: "Yes, Indira Dental Clinic offers flexible payment plans to help make dental care more affordable. We understand that quality dental treatment is an investment, and our team works with patients to create payment arrangements that fit their budget. Please discuss payment options with our staff during your visit."
      },
      {
        question: "What is your cancellation policy?",
        answer: "We request at least 24 hours' notice for appointment cancellations or rescheduling. This allows us to offer the appointment time to other patients who may need urgent care. Repeated late cancellations or missed appointments may result in a cancellation fee. We understand that emergencies happen, so please contact our office at ********** as soon as possible if you need to change your appointment."
      },
      {
        question: "How much will my treatment cost?",
        answer: "Treatment costs vary depending on the specific procedures needed, insurance coverage, and individual circumstances. At Indira Dental Clinic, Dr. Rockson Samuel and our team provide detailed treatment plans with cost estimates before beginning any treatment. We believe in transparent pricing and will discuss all financial aspects of your care upfront."
      },
      {
        question: "Do you offer senior discounts?",
        answer: "Yes, Indira Dental Clinic offers special considerations for our senior patients. We understand that many seniors are on fixed incomes and may have specific dental needs. Please inquire about our senior programs and discounts when scheduling your appointment with Dr. Rockson Samuel."
      }
    ],
    appointments: [
      {
        question: "How do I schedule an appointment?",
        answer: "You can schedule an appointment with Dr. Rockson Samuel at Indira Dental Clinic by calling our office at ********** during business hours. Alternatively, you can use our online appointment request form on our website. Our staff will follow up to confirm your appointment time and answer any questions you may have."
      },
      {
        question: "What should I bring to my first appointment?",
        answer: "For your first visit to Indira Dental Clinic, please bring your identification, insurance card (if applicable), a list of current medications, and your complete medical and dental history. If you have recent dental X-rays from a previous dentist, bringing those can be helpful. Arriving 15 minutes early to complete any necessary paperwork is recommended."
      },
      {
        question: "How long does a typical appointment take?",
        answer: "The duration of appointments at Indira Dental Clinic varies depending on the type of visit. Routine check-ups and cleanings typically take 45-60 minutes, while more complex procedures may require longer appointments. Dr. Rockson Samuel values thoroughness and quality care, ensuring each patient receives the time and attention they need."
      },
      {
        question: "Do you see patients on weekends?",
        answer: "Indira Dental Clinic is open on Sundays from 10 am to 1:30 pm to accommodate patients who cannot visit during weekdays. Our weekday hours are 10 am to 8 pm, Monday through Saturday. Please call our office at ********** to check availability and schedule your appointment with Dr. Rockson Samuel."
      },
      {
        question: "How often should I have a dental check-up and cleaning?",
        answer: "Most patients should have a dental check-up and professional cleaning every six months. However, Dr. Rockson Samuel may recommend more frequent visits based on your specific oral health needs, such as if you have gum disease, a high risk of cavities, or other ongoing dental issues. Regular preventive care at Indira Dental Clinic helps maintain optimal oral health and detect problems early."
      }
    ],
    emergency: [
      {
        question: "What should I do in case of a dental emergency?",
        answer: "In case of a dental emergency, contact Indira Dental Clinic immediately at **********. We reserve time in our daily schedule for emergency patients. For after-hours emergencies, our answering service will direct you to Dr. Rockson Samuel or the on-call dentist. For severe injuries or uncontrolled bleeding, please go to the nearest emergency room."
      },
      {
        question: "What should I do if I knock out a tooth?",
        answer: "If you knock out a tooth, retrieve it by the crown (not the root), rinse it gently without scrubbing, and try to reinsert it into the socket. If that's not possible, place the tooth in milk or a tooth preservation solution and seek immediate dental care at Indira Dental Clinic. Time is critical – the tooth has the best chance of being saved if you see Dr. Rockson Samuel within 30-60 minutes."
      },
      {
        question: "How should I handle a cracked or broken tooth?",
        answer: "For a cracked or broken tooth, rinse your mouth with warm water, apply a cold compress to reduce swelling, and take over-the-counter pain relievers if needed. Contact Indira Dental Clinic at ********** as soon as possible to schedule an emergency appointment with Dr. Rockson Samuel. Avoid chewing on the affected side until you receive treatment."
      },
      {
        question: "What constitutes a dental emergency?",
        answer: "Dental emergencies include severe toothache, knocked-out teeth, loose or displaced teeth, broken teeth with pain, injuries to the soft tissues of the mouth, abscess or infection with swelling, and broken dental appliances that cause pain or injury. If you're experiencing any of these issues, contact Indira Dental Clinic immediately at ********** to speak with Dr. Rockson Samuel or our staff."
      },
      {
        question: "How can I manage tooth pain until my emergency appointment?",
        answer: "To manage tooth pain until your emergency appointment at Indira Dental Clinic, rinse with warm salt water, take over-the-counter pain relievers as directed (avoid aspirin for bleeding issues), apply a cold compress to reduce swelling, and use clove oil on the affected area for temporary relief. Avoid very hot, cold, or sweet foods and beverages that may worsen the pain. Dr. Rockson Samuel will address the underlying cause during your emergency visit."
      }
    ]
  };

  return (
    <>
      {/* CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>

      <div className="min-h-screen" style={{ backgroundColor: '#FDFDFD' }}>
        {/* Hero Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6" 
              style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
            Frequently Asked Questions
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto" 
             style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
            Find answers to the most common questions about dental care, treatments, 
            and our services at Indira Dental Clinic.
          </p>
        </div>
      </section>

      {/* Category Navigation */}
      <section className="py-8 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FFF5F8' }}>
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 md:gap-3">
            {faqCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-3 md:px-4 py-2 md:py-3 rounded-lg text-sm md:text-base font-medium transition-all duration-300 border-2 hover:shadow-md ${
                  activeCategory === category.id
                    ? 'text-white shadow-lg hover:shadow-xl'
                    : 'text-[#2b2b2b] hover:text-[#E53274] hover:border-[#E53274]'
                }`}
                style={{
                  fontFamily: 'Inter, sans-serif',
                  backgroundColor: activeCategory === category.id ? '#E53274' : '#FDFDFD',
                  borderColor: activeCategory === category.id ? '#E53274' : '#93D214'
                }}
              >
                <span className="mr-2">{category.icon}</span>
                <span className="hidden sm:inline">{category.name}</span>
                <span className="sm:hidden">{category.name.split(' ')[0]}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              {faqCategories.find(cat => cat.id === activeCategory)?.name || 'FAQ'}
            </h2>
            <div className="w-24 h-1 mx-auto rounded-full" style={{ backgroundColor: '#93D214' }}></div>
          </div>

          <div className="space-y-4">
            {faqData[activeCategory]?.map((faq, index) => (
              <div key={index}
                   className="border-2 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02] cursor-pointer"
                   style={{
                     backgroundColor: '#FFF5F8',
                     borderColor: '#E53274',
                     boxShadow: '5px 5px 4px 0px #00000040'
                   }}>
                <button
                  className="w-full px-6 py-5 text-left flex justify-between items-center focus:outline-none hover:bg-opacity-80 transition-all duration-300"
                  style={{ backgroundColor: 'transparent' }}
                  onClick={() => toggleFAQ(index)}
                >
                  <h3 className="text-lg md:text-xl font-semibold pr-4"
                      style={{
                        fontFamily: 'DM Sans, sans-serif',
                        color: '#2b2b2b'
                      }}>
                    {faq.question}
                  </h3>
                  <svg
                    className={`w-6 h-6 transition-transform duration-300 flex-shrink-0 ${openFAQ === index ? 'rotate-180' : ''}`}
                    style={{ color: '#E53274' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {openFAQ === index && (
                  <div className="px-6 pb-5 border-t-2 animate-fadeIn"
                       style={{
                         backgroundColor: '#FDFDFD',
                         borderColor: '#E53274'
                       }}>
                    <p className="text-base md:text-lg leading-relaxed pt-4"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b',
                         fontWeight: '400',
                         lineHeight: '1.7'
                       }}>
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white rounded-2xl p-8 md:p-12 border-2 shadow-lg"
                 style={{ borderColor: '#E53274' }}>
              <h2 className="text-2xl md:text-3xl font-bold mb-6"
                  style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                Still have questions? We're here to help!
              </h2>
              <p className="text-lg mb-8"
                 style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                Contact our team at Indira Dental Clinic for personalized answers to your dental questions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact"
                      className="inline-block bg-[#E53274] hover:bg-[#C7205D] text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg"
                      style={{ fontFamily: 'Inter, sans-serif' }}>
                  Contact Us
                </Link>
                <BookAppointmentButton size="lg" />
              </div>
            </div>
          </div>
        </section>

        {/* Contact Info Section */}
        <ContactInfoSection />
      </div>
    </>
  );
}
