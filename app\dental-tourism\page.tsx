'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import BookAppointmentButton from '@/components/ui/BookAppointmentButton';
import CallBackForm from '@/components/ui/CallBackForm';
import { ServiceCardData } from '@/types/services';
import { getHomepageServices } from '@/utils/services-api';

export default function DentalTourismPage() {
  // FAQ state management
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  // Services state management
  const [servicesData, setServicesData] = useState<ServiceCardData[]>([]);
  const [servicesLoading, setServicesLoading] = useState(true);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // Scroll to contact form function
  const scrollToContactForm = () => {
    const contactSection = document.getElementById('contact-form');
    if (contactSection) {
      contactSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Consistent Call Back Button Component
  const CallBackButton = ({ children, className = "", onClick }: {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
  }) => (
    <button
      className={`bg-[#E53274] hover:bg-[#C7205D] text-[#FDFDFD] flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 text-base font-semibold hover:shadow-lg hover:scale-105 border-2 border-white ${className}`}
      style={{ fontFamily: 'DM Sans, sans-serif' }}
      onClick={onClick || scrollToContactForm}
    >
      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
      </svg>
      {children}
    </button>
  );

  // FAQ data
  const faqData = [
    {
      question: "What is dental tourism and why choose India?",
      answer: "Dental tourism involves traveling to another country for dental care. India offers world-class dental treatments at 60-80% lower costs than Western countries, with internationally trained dentists and state-of-the-art facilities."
    },
    {
      question: "How much can I save with dental tourism in India?",
      answer: "You can save 60-80% on dental procedures compared to US, UK, or Australian prices. For example, dental implants that cost $3,000-5,000 in the US can be done for $800-1,500 in India, including accommodation and travel costs."
    },
    {
      question: "Is the quality of dental care in India comparable to international standards?",
      answer: "Yes, many Indian dental clinics follow international standards with JCI accreditation. Our dentists are trained internationally and use the same equipment and materials as clinics in developed countries."
    },
    {
      question: "What dental procedures are popular for dental tourism?",
      answer: "Popular procedures include dental implants, full mouth rehabilitation, cosmetic dentistry, root canal treatments, crowns and bridges, orthodontics (braces), and smile makeovers."
    },
    {
      question: "How long should I plan to stay for dental treatment?",
      answer: "Depending on the procedure: simple treatments (1-3 days), dental implants (5-7 days), full mouth rehabilitation (10-14 days). We provide detailed treatment timelines during consultation."
    },
    {
      question: "Do you provide assistance with travel and accommodation?",
      answer: "Yes, we assist with travel planning, airport transfers, accommodation recommendations, and local sightseeing. We can arrange comfortable stays near our clinic for your convenience."
    }
  ];

  // Fetch services data
  useEffect(() => {
    const fetchServicesData = async () => {
      try {
        setServicesLoading(true);
        const data = await getHomepageServices();
        setServicesData(data);
      } catch (error) {
        console.error('Error fetching services data:', error);
      } finally {
        setServicesLoading(false);
      }
    };

    fetchServicesData();
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section
        className="relative py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8"
        style={{
          background: 'linear-gradient(to bottom, #E53274 0%, #E53274 70%, #575C8D 100%)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Content */}
            <div className="text-[#FDFDFD] text-center lg:text-left">
              <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-6" 
                  style={{ fontFamily: 'DM Sans', fontWeight: 700 }}>
                Dental Tourism in Vellore
              </h1>
              <p className="text-lg md:text-xl lg:text-2xl mb-6" 
                 style={{ fontFamily: 'Inter', fontWeight: 500 }}>
                Save 60-80% on world-class dental treatments
              </p>
              <p className="text-base md:text-lg mb-8 max-w-2xl mx-auto lg:mx-0"
                 style={{ fontFamily: 'Inter', fontWeight: 400 }}>
                Experience premium dental care at Indira Dental Clinic, Vellore. International-standard treatments,
                English-speaking dentists, and comprehensive travel assistance for international patients.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <BookAppointmentButton fullWidth className="sm:w-auto">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  Book Appointment
                </BookAppointmentButton>
                <CallBackButton>
                  Plan Your Dental Trip
                </CallBackButton>
              </div>
            </div>

            {/* Right Image */}
            <div className="flex justify-center lg:justify-end">
              <Image
                src="/dental-tourism-hero.jpg"
                alt="International patients receiving dental care at Indira Dental Clinic"
                width={600}
                height={500}
                className="w-full max-w-md lg:max-w-lg xl:max-w-xl h-auto object-cover rounded-lg shadow-lg"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose India for Dental Tourism */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Why Choose Indira Dental Clinic for Dental Tourism?
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              Indira Dental Clinic is in the heart of Vellore which has emerged as a leading destination for dental tourism, offering world-class treatments 
              at a fraction of the cost you'd pay in Western countries.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {/* Cost Savings Card */}
            <div className="rounded-xl p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
                 style={{ backgroundColor: '#FFF5F8', borderColor: '#E53274' }}>
              <div className="flex justify-center mb-4">
                <Image src="/rupee.svg" alt="Cost Savings" width={48} height={48} className="w-12 h-12" />
              </div>
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#2b2b2b' }}>
                Massive Cost Savings
              </h3>
              <p className="text-base" style={{ fontFamily: 'Inter', fontWeight: 400, color: '#2b2b2b' }}>
                Save 60-80% on dental procedures compared to US, UK, or Australian prices, even including travel costs.
              </p>
            </div>

            {/* Quality Care Card */}
            <div className="rounded-xl p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
                 style={{ backgroundColor: '#FFF5F8', borderColor: '#E53274' }}>
              <div className="flex justify-center mb-4">
                <Image src="/dentist.svg" alt="Quality Care" width={48} height={48} className="w-12 h-12" />
              </div>
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#2b2b2b' }}>
                International Standards
              </h3>
              <p className="text-base" style={{ fontFamily: 'Inter', fontWeight: 400, color: '#2b2b2b' }}>
                World-class facilities with internationally trained dentists using the latest technology and materials.
              </p>
            </div>

            {/* English Speaking Card */}
            <div className="rounded-xl p-6 lg:p-8 text-center border-2 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
                 style={{ backgroundColor: '#FFF5F8', borderColor: '#E53274' }}>
              <div className="flex justify-center mb-4">
                <Image src="/globe.svg" alt="English Speaking" width={48} height={48} className="w-12 h-12" />
              </div>
              <h3 className="text-xl font-bold mb-4" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#2b2b2b' }}>
                English Speaking
              </h3>
              <p className="text-base" style={{ fontFamily: 'Inter', fontWeight: 400, color: '#2b2b2b' }}>
                Clear communication with English-speaking dentists and staff, ensuring you understand every step.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Cost Comparison Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Cost Comparison
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              See how much you can save by choosing dental treatment at Indira Dental Clinic
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white rounded-lg shadow-lg overflow-hidden">
              <thead style={{ backgroundColor: '#f8f9fa', borderBottom: '2px solid #e9ecef' }}>
                <tr>
                  <th className="px-6 py-4 text-left font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                    Procedure
                  </th>
                  <th className="px-6 py-4 text-center font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                    USA/UK/Australia (USD)
                  </th>
                  <th className="px-6 py-4 text-center font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                    Indira Dental Clinic (USD)
                  </th>
                  <th className="px-6 py-4 text-center font-bold" style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                    Savings
                  </th>
                </tr>
              </thead>
              <tbody>
                {[
                  { procedure: 'Dental Implant (single)', international: '$3,000 - $4,500', indira: '$800 - $1,200', savings: 'Up to 75%' },
                  { procedure: 'Porcelain Crown', international: '$1,000 - $1,500', indira: '$250 - $400', savings: 'Up to 75%' },
                  { procedure: 'Root Canal Treatment', international: '$700 - $1,200', indira: '$150 - $300', savings: 'Up to 80%' },
                  { procedure: 'Porcelain Veneer (per tooth)', international: '$900 - $2,500', indira: '$250 - $500', savings: 'Up to 80%' },
                  { procedure: 'Full Mouth Rehabilitation', international: '$20,000 - $45,000', indira: '$5,000 - $10,000', savings: 'Up to 80%' }
                ].map((row, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="px-6 py-4 font-semibold" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                      {row.procedure}
                    </td>
                    <td className="px-6 py-4 text-center" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                      {row.international}
                    </td>
                    <td className="px-6 py-4 text-center font-bold" style={{ fontFamily: 'Inter, sans-serif', color: '#E53274' }}>
                      {row.indira}
                    </td>
                    <td className="px-6 py-4 text-center font-bold" style={{ fontFamily: 'Inter, sans-serif', color: '#28a745' }}>
                      {row.savings}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                * Prices are approximate and may vary based on individual cases. Please contact us for a personalized quote.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FFF5F8' }}>
        <div className="max-w-7xl mx-auto">
          {/* Section Title */}
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6"
                style={{
                  fontFamily: 'DM Sans, sans-serif',
                  color: '#E53274'
                }}>
              Our Services
            </h2>
            <p className="text-base md:text-lg lg:text-xl max-w-3xl mx-auto px-4"
               style={{
                 fontFamily: 'Inter, sans-serif',
                 color: '#2b2b2b',
                 fontWeight: '400'
               }}>
              We offer complete dental care in Vellore — all painless, clearly priced, and tailored to your needs, perfect for international patients seeking quality dental tourism.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
            {servicesLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="p-4 md:p-6 lg:p-8 rounded-lg animate-pulse"
                     style={{
                       backgroundColor: '#FDFDFD',
                       boxShadow: '10px 10px 4px 0px #00000040'
                     }}>
                  <div className="flex justify-center mb-3 md:mb-4">
                    <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full bg-gray-200"
                         style={{ borderColor: '#93D214' }}>
                    </div>
                  </div>
                  <div className="h-5 md:h-6 bg-gray-200 rounded mb-2 md:mb-3"></div>
                  <div className="h-3 md:h-4 bg-gray-200 rounded"></div>
                </div>
              ))
            ) : (
              // Dynamic service cards
              servicesData.map((service) => (
                <Link key={service.id} href={`/services/${service.slug}`}>
                  <div className="p-4 md:p-6 lg:p-8 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer group"
                       style={{
                         backgroundColor: '#FDFDFD',
                         boxShadow: '10px 10px 4px 0px #00000040'
                       }}>
                    <div className="flex justify-center mb-3 md:mb-4">
                      <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full flex items-center justify-center border-3 md:border-4 transition-all duration-300 group-hover:border-[#E53274]"
                           style={{ borderColor: '#93D214' }}>
                        <Image
                          src={service.icon}
                          alt={`${service.title} service icon`}
                          width={32}
                          height={32}
                          className="w-5 h-5 md:w-6 md:h-6 lg:w-8 lg:h-8"
                        />
                      </div>
                    </div>
                    <h3 className="text-base md:text-lg lg:text-xl font-bold text-center mb-2 md:mb-3"
                        style={{
                          fontFamily: 'DM Sans, sans-serif',
                          color: '#2b2b2b'
                        }}>
                      {service.title}
                    </h3>
                    <p className="text-xs md:text-sm lg:text-base text-center"
                       style={{
                         fontFamily: 'Inter, sans-serif',
                         color: '#2b2b2b',
                         fontWeight: '400'
                       }}>
                      {service.excerpt}
                    </p>
                  </div>
                </Link>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Why Choose Indira Dental Clinic for Dental Tourism */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Why Choose Indira Dental Clinic for Dental Tourism?
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              We provide comprehensive support for international patients seeking quality dental care in India
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left - Image */}
            <div className="flex justify-center lg:justify-start">
              <Image
                src="/dental-tourism-clinic.jpg"
                alt="Modern dental clinic facility for international patients"
                width={600}
                height={500}
                className="w-full max-w-lg h-auto object-cover rounded-lg shadow-lg"
              />
            </div>

            {/* Right - Features List */}
            <div className="space-y-6">
              {[
                "Internationally trained dentist: Dr. Rockson Samuel (BDS, MDS)",
                "State-of-the-art equipment and sterilization protocols",
                "English-speaking staff and clear communication",
                "Comprehensive treatment planning and cost estimates",
                "Airport pickup and drop-off services",
                "Assistance with accommodation near the clinic",
                "Local sightseeing and travel guidance",
                "Follow-up care and treatment warranties",
                "Digital X-rays and treatment documentation",
                "Flexible appointment scheduling for tourists"
              ].map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="w-6 h-6" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-lg" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
                    {feature}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Treatment Timeline Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F3F4FA' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Treatment Timeline & Planning
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              Plan your dental tourism trip with our comprehensive timeline guide
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 md:gap-8">
            {/* Before Your Visit */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-[#E53274] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl" style={{ fontFamily: 'DM Sans, sans-serif' }}>1</span>
                </div>
                <h3 className="text-xl font-bold" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#E53274' }}>
                  Before Your Visit
                </h3>
              </div>
              <ul className="space-y-3 text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                <li>• Free online consultation</li>
                <li>• Treatment plan & cost estimate</li>
                <li>• Travel assistance & booking</li>
                <li>• Accommodation arrangements</li>
                <li>• Airport transfer coordination</li>
              </ul>
            </div>

            {/* During Treatment */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-[#E53274] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl" style={{ fontFamily: 'DM Sans, sans-serif' }}>2</span>
                </div>
                <h3 className="text-xl font-bold" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#E53274' }}>
                  During Treatment
                </h3>
              </div>
              <ul className="space-y-3 text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                <li>• Comprehensive examination</li>
                <li>• Digital X-rays & diagnostics</li>
                <li>• Treatment execution</li>
                <li>• Daily progress monitoring</li>
                <li>• Local sightseeing assistance</li>
              </ul>
            </div>

            {/* After Treatment */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-[#E53274] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl" style={{ fontFamily: 'DM Sans, sans-serif' }}>3</span>
                </div>
                <h3 className="text-xl font-bold" style={{ fontFamily: 'DM Sans', fontWeight: 700, color: '#E53274' }}>
                  After Treatment
                </h3>
              </div>
              <ul className="space-y-3 text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                <li>• Treatment documentation</li>
                <li>• Care instructions</li>
                <li>• Follow-up appointments</li>
                <li>• Remote monitoring support</li>
                <li>• Treatment warranty</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Frequently Asked Questions
            </h2>
            <p className="text-lg md:text-xl"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              Everything you need to know about dental tourism in India
            </p>
          </div>

          <div className="space-y-4">
            {faqData.map((faq, index) => (
              <div key={index} className="border-2 rounded-lg overflow-hidden" style={{ borderColor: '#93D214' }}>
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => toggleFAQ(index)}
                >
                  <h3 className="text-lg font-semibold pr-4"
                      style={{ fontFamily: 'DM Sans, sans-serif', color: '#2b2b2b' }}>
                    {faq.question}
                  </h3>
                  <svg
                    className={`w-6 h-6 transform transition-transform duration-200 ${openFAQ === index ? 'rotate-180' : ''}`}
                    style={{ color: '#E53274' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {openFAQ === index && (
                  <div className="px-6 pb-4">
                    <p className="text-base leading-relaxed"
                       style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="contact-form" className="py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#F1F2F8' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
              Plan Your Dental Tourism Trip
            </h2>
            <p className="text-lg md:text-xl max-w-4xl mx-auto mb-8"
               style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b', fontWeight: '400' }}>
              Get a free consultation and personalized treatment plan for your dental tourism journey to Vellore
            </p>
            <div className="flex justify-center">
              <CallBackButton>
                Get A Call Back
              </CallBackButton>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
            {/* Left - Contact Form */}
            <div className="flex justify-center">
              <CallBackForm variant="contact" />
            </div>

            {/* Right - Travel Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Travel to Vellore
                </h3>
                <div className="space-y-4 text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  <p><strong>Nearest Airport:</strong> Chennai International Airport (120 km)</p>
                  <p><strong>Nearest Railway Station:</strong> Katpadi Railway Station (2 km)</p>
                  <p><strong>Local Transport:</strong> Auto-rickshaws, taxis, and buses available</p>
                  <p><strong>Languages Spoken:</strong> English, Tamil, Hindi</p>
                </div>
              </div>

              <div>
                <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  Accommodation Options
                </h3>
                <div className="space-y-4 text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                  <p><strong>Budget Hotels:</strong> ₹1,500-3,000 per night</p>
                  <p><strong>Mid-range Hotels:</strong> ₹3,000-6,000 per night</p>
                  <p><strong>Luxury Hotels:</strong> ₹6,000+ per night</p>
                  <p><strong>Service Apartments:</strong> ₹2,500-5,000 per night</p>
                </div>
              </div>

              <div>
                <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'DM Sans, sans-serif', color: '#E53274' }}>
                  What's Included
                </h3>
                <div className="space-y-2">
                  {[
                    "Free initial consultation",
                    "Detailed treatment plan",
                    "Airport pickup & drop-off",
                    "Accommodation assistance",
                    "Local transportation guidance",
                    "Sightseeing recommendations",
                    "24/7 emergency support"
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <svg className="w-5 h-5" style={{ color: '#93D214' }} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base" style={{ fontFamily: 'Inter, sans-serif', color: '#2b2b2b' }}>
                        {item}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
