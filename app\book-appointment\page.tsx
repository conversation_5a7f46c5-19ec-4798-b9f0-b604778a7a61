'use client';

import { useEffect } from 'react';

export default function BookAppointmentPage() {
  useEffect(() => {
    // Redirect to the external booking URL
    window.location.href = 'https://app.dentalclinicinvellore.in/booking/';
  }, []);

  return (
    <div className="w-full min-h-screen flex items-center justify-center bg-white">
      {/* Loading state while redirecting */}
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274] mb-4"></div>
        <p className="text-lg font-semibold text-[#E53274]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
          Redirecting to Booking...
        </p>
        <p className="text-sm text-gray-600 mt-2" style={{ fontFamily: 'Inter, sans-serif' }}>
          You will be redirected to our booking system
        </p>
      </div>
    </div>
  );
}
