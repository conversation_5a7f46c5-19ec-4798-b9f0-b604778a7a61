'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { LegalPageData } from '@/types/legal-pages';
import { getLegalPageBySlug } from '@/utils/legal-pages-api';
import ContactInfoSection from '@/components/ui/ContactInfoSection';

export default function LegalPagePost() {
  const params = useParams();
  const slug = params.slug as string;
  const [page, setPage] = useState<LegalPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPage = async () => {
      try {
        setLoading(true);
        const pageData = await getLegalPageBySlug(slug);
        if (pageData) {
          setPage(pageData);
        } else {
          setError('Legal page not found');
        }
      } catch (err) {
        console.error('Error loading legal page:', err);
        setError('Failed to load legal page');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      loadPage();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274]"></div>
          <p className="mt-4 text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
            Loading legal page...
          </p>
        </div>
      </div>
    );
  }

  if (error || !page) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {error || 'Legal page not found'}
          </h1>
          <Link 
            href="/"
            className="text-[#E53274] hover:underline"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8" 
               style={{ 
                 background: 'linear-gradient(135deg, #E53274 0%, #575C8D 100%)'
               }}>
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6"
              style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {page.title}
          </h1>
          {page.excerpt && (
            <p className="text-lg md:text-xl text-white/90 max-w-3xl mx-auto"
               style={{ fontFamily: 'Inter, sans-serif' }}>
              {page.excerpt}
            </p>
          )}
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 md:p-12 shadow-sm border border-gray-100">
            {/* Breadcrumb */}
            <nav className="mb-8 pb-6 border-b border-gray-100">
              <div className="flex items-center space-x-2 text-sm">
                <Link 
                  href="/"
                  className="text-[#575C8D] hover:text-[#E53274] transition-colors"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Home
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-[#2b2b2b]" style={{ fontFamily: 'Inter, sans-serif' }}>
                  {page.title}
                </span>
              </div>
            </nav>

            {/* Content */}
            <div
              className="prose prose-lg max-w-none legal-content"
              style={{
                fontFamily: 'Inter, sans-serif',
                color: '#2b2b2b',
                lineHeight: '1.8'
              }}
              dangerouslySetInnerHTML={{ __html: page.content || '' }}
            />

            <style jsx>{`
              .legal-content h1,
              .legal-content h2,
              .legal-content h3,
              .legal-content h4,
              .legal-content h5,
              .legal-content h6 {
                font-family: 'DM Sans', sans-serif;
                font-weight: 700;
                color: #E53274;
                margin-top: 2rem;
                margin-bottom: 1rem;
              }

              .legal-content h1 {
                font-size: 2.25rem;
                line-height: 2.5rem;
              }

              .legal-content h2 {
                font-size: 1.875rem;
                line-height: 2.25rem;
              }

              .legal-content h3 {
                font-size: 1.5rem;
                line-height: 2rem;
              }

              .legal-content p {
                margin-bottom: 1.5rem;
                font-size: 1.125rem;
                line-height: 1.8;
              }

              .legal-content ul,
              .legal-content ol {
                margin-bottom: 1.5rem;
                padding-left: 2rem;
              }

              .legal-content li {
                margin-bottom: 0.5rem;
                font-size: 1.125rem;
                line-height: 1.7;
              }

              .legal-content strong {
                font-weight: 600;
                color: #575C8D;
              }

              .legal-content a {
                color: #E53274;
                text-decoration: underline;
              }

              .legal-content a:hover {
                color: #C7205D;
              }

              .legal-content blockquote {
                border-left: 4px solid #E53274;
                padding-left: 1.5rem;
                margin: 2rem 0;
                font-style: italic;
                background-color: #FFF5F8;
                padding: 1.5rem;
                border-radius: 0.5rem;
              }

              .legal-content table {
                width: 100%;
                border-collapse: collapse;
                margin: 2rem 0;
              }

              .legal-content th,
              .legal-content td {
                border: 1px solid #E5E7EB;
                padding: 0.75rem;
                text-align: left;
              }

              .legal-content th {
                background-color: #F3F4FA;
                font-weight: 600;
                color: #575C8D;
              }
            `}</style>

            {/* Back to Home Button */}
            <div className="mt-12 pt-8 border-t border-gray-100 text-center">
              <Link 
                href="/"
                className="inline-flex items-center gap-2 px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 hover:scale-105 hover:shadow-lg"
                style={{
                  backgroundColor: '#E53274',
                  fontFamily: 'DM Sans, sans-serif'
                }}
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd"/>
                </svg>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <ContactInfoSection />
    </div>
  );
}
