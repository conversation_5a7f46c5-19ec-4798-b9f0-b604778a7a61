'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { OfferCardData } from '@/types/offers';
import { getAllOffers } from '@/utils/offers-api';
import ContactInfoSection from '@/components/ui/ContactInfoSection';
import OfferCard from '@/components/ui/OfferCard';

export default function OffersPage() {
  const [offers, setOffers] = useState<OfferCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOffers = async () => {
      try {
        setLoading(true);
        console.log('Fetching offers...'); // Debug log
        const offersData = await getAllOffers();
        console.log('Offers data received:', offersData); // Debug log
        setOffers(offersData);
      } catch (err) {
        console.error('Error fetching offers:', err);
        setError('Failed to load offers. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, []);

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#E53274] mb-4"></div>
          <p className="text-lg font-semibold text-[#E53274]" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Loading Offers...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <p className="text-lg font-semibold text-red-600 mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-[#E53274] hover:bg-[#C7205D] text-white px-6 py-2 rounded-md transition-colors"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-[#E53274] via-[#E53274] to-[#575C8D]">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#FDFDFD] mb-6" style={{ fontFamily: 'DM Sans, sans-serif' }}>
            Special Dental Offers
          </h1>
          <p className="text-lg md:text-xl text-[#FDFDFD] max-w-3xl mx-auto leading-relaxed" style={{ fontFamily: 'Inter, sans-serif' }}>
            Welcome to Indira Dental Clinic's offers! Here, you'll find special promotions, discounts, and package deals for a range of dental services, including routine checkups, cleanings, orthodontics, and cosmetic dentistry. We offer affordable options without compromising on quality. Our aim is to provide the best dental care in a welcoming environment.
          </p>
        </div>
      </section>

      {/* Offers Grid Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#FDFDFD' }}>
        <div className="max-w-6xl mx-auto">
          {offers.length === 0 ? (
            <div className="text-center py-16">
              <div className="mb-8">
                <svg className="w-24 h-24 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-[#2b2b2b] mb-4" style={{ fontFamily: 'DM Sans, sans-serif' }}>
                No Offers Available
              </h3>
              <p className="text-lg text-[#2b2b2b] mb-8" style={{ fontFamily: 'Inter, sans-serif' }}>
                We're currently updating our offers. Please check back soon for exciting dental care promotions!
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center gap-2 bg-[#E53274] hover:bg-[#C7205D] text-white px-6 py-3 rounded-md transition-colors"
                style={{ fontFamily: 'Inter, sans-serif' }}
              >
                Contact Us for Current Promotions
              </Link>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {offers.map((offer) => (
                <OfferCard key={offer.id} offer={offer} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Contact Info Section */}
      <ContactInfoSection />
    </div>
  );
}
